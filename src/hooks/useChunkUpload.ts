import { useState, useCallback, useRef } from 'react';
import { message } from 'antd';
import {
  initChunkUpload,
  uploadChunk,
  mergeChunks,
  cancelChunkUpload,
  getUploadProgress,
  ChunkUploadInitParams,
  ChunkUploadParams,
  ChunkMergeParams
} from '@/api/fileManagement';
import {
  calculateFileMD5,
  calculateChunkMD5,
  createFileChunks,
  getRecommendedChunkSize,
  UploadStatus,
  UploadTask,
  calculateUploadSpeed,
  withRetry,
  DEFAULT_RETRY_CONFIG
} from '@/utils/chunkUpload';

export interface UseChunkUploadOptions {
  chunkSize?: number;
  maxConcurrency?: number; // 最大并发数
  enableRetry?: boolean;
  retryConfig?: typeof DEFAULT_RETRY_CONFIG;
  onProgress?: (task: UploadTask) => void;
  onSuccess?: (task: UploadTask, fileUrl: string) => void;
  onError?: (task: UploadTask, error: string) => void;
  onStatusChange?: (task: UploadTask) => void;
}

export interface UseChunkUploadReturn {
  uploadFile: (file: File, options?: Partial<ChunkUploadInitParams>) => Promise<string>;
  pauseUpload: (taskId: string) => void;
  resumeUpload: (taskId: string) => void;
  cancelUpload: (taskId: string) => void;
  getTask: (taskId: string) => UploadTask | undefined;
  getAllTasks: () => UploadTask[];
  clearCompletedTasks: () => void;
}

export const useChunkUpload = (options: UseChunkUploadOptions = {}): UseChunkUploadReturn => {
  const {
    maxConcurrency = 3,
    enableRetry = true,
    retryConfig = DEFAULT_RETRY_CONFIG,
    onProgress,
    onSuccess,
    onError,
    onStatusChange
  } = options;

  const [tasks, setTasks] = useState<Map<string, UploadTask>>(new Map());
  const uploadingTasks = useRef<Set<string>>(new Set());
  const abortControllers = useRef<Map<string, AbortController>>(new Map());

  // 更新任务状态
  const updateTask = useCallback((taskId: string, updates: Partial<UploadTask>) => {
    setTasks(prev => {
      const newTasks = new Map(prev);
      const task = newTasks.get(taskId);
      if (task) {
        const updatedTask = { ...task, ...updates };
        newTasks.set(taskId, updatedTask);
        onStatusChange?.(updatedTask);
        return newTasks;
      }
      return prev;
    });
  }, [onStatusChange]);

  // 上传单个分片
  const uploadSingleChunk = useCallback(async (
    task: UploadTask,
    chunkIndex: number,
    chunk: Blob,
    abortSignal?: AbortSignal
  ): Promise<void> => {
    if (abortSignal?.aborted) {
      throw new Error('Upload cancelled');
    }

    const chunkMd5 = await calculateChunkMD5(chunk);
    
    const uploadParams: ChunkUploadParams = {
      uploadId: task.uploadId!,
      chunkNumber: chunkIndex,
      chunkFile: chunk,
      chunkMd5
    };

    const uploadFn = async () => {
      if (abortSignal?.aborted) {
        throw new Error('Upload cancelled');
      }
      return await uploadChunk(uploadParams);
    };

    const response = enableRetry 
      ? await withRetry(uploadFn, retryConfig)
      : await uploadFn();

    if (response.data.success) {
      // 更新任务进度
      updateTask(task.id, {
        uploadedChunks: response.data.uploadedChunks,
        progress: response.data.progress
      });

      // 触发进度回调
      const updatedTask = tasks.get(task.id);
      if (updatedTask) {
        onProgress?.(updatedTask);
      }
    } else {
      throw new Error(response.data.message || '分片上传失败');
    }
  }, [tasks, updateTask, onProgress, enableRetry, retryConfig]);

  // 并发上传分片
  const uploadChunks = useCallback(async (task: UploadTask, chunks: Blob[]): Promise<void> => {
    const abortController = abortControllers.current.get(task.id);
    const abortSignal = abortController?.signal;

    // 获取已上传的分片
    const progressResponse = await getUploadProgress(task.uploadId!);
    const uploadedChunkNumbers = new Set<number>();
    
    // 假设API返回已上传分片的索引数组
    for (let i = 0; i < progressResponse.data.uploadedChunks; i++) {
      uploadedChunkNumbers.add(i);
    }

    // 过滤出需要上传的分片
    const chunksToUpload = chunks
      .map((chunk, index) => ({ chunk, index }))
      .filter(({ index }) => !uploadedChunkNumbers.has(index));

    // 并发上传分片
    const uploadPromises: Promise<void>[] = [];
    let activeUploads = 0;
    let chunkIndex = 0;

    const uploadNext = async (): Promise<void> => {
      while (chunkIndex < chunksToUpload.length && activeUploads < maxConcurrency) {
        if (abortSignal?.aborted) {
          throw new Error('Upload cancelled');
        }

        const { chunk, index } = chunksToUpload[chunkIndex++];
        activeUploads++;

        const uploadPromise = uploadSingleChunk(task, index, chunk, abortSignal)
          .finally(() => {
            activeUploads--;
          });

        uploadPromises.push(uploadPromise);

        // 如果还有分片要上传，继续
        if (chunkIndex < chunksToUpload.length) {
          uploadPromise.then(() => uploadNext()).catch(() => {});
        }
      }
    };

    await uploadNext();
    await Promise.all(uploadPromises);
  }, [maxConcurrency, uploadSingleChunk]);

  // 主上传函数
  const uploadFile = useCallback(async (
    file: File, 
    initOptions: Partial<ChunkUploadInitParams> = {}
  ): Promise<string> => {
    const taskId = `${file.name}_${Date.now()}`;
    
    // 创建上传任务
    const chunkSize = options.chunkSize || getRecommendedChunkSize(file.size);
    const chunks = createFileChunks(file, chunkSize);
    
    const task: UploadTask = {
      id: taskId,
      file,
      status: UploadStatus.PENDING,
      progress: 0,
      uploadedChunks: 0,
      totalChunks: chunks.length,
      chunkSize,
      startTime: Date.now()
    };

    setTasks(prev => new Map(prev.set(taskId, task)));
    onStatusChange?.(task);

    try {
      // 计算文件MD5
      updateTask(taskId, { status: UploadStatus.UPLOADING });
      const fileMd5 = await calculateFileMD5(file);
      updateTask(taskId, { fileMd5 });

      // 初始化上传
      const initParams: ChunkUploadInitParams = {
        fileName: file.name,
        fileMd5,
        totalSize: file.size,
        chunkSize,
        fileType: file.type,
        ...initOptions
      };

      const initResponse = await initChunkUpload(initParams);
      
      if (!initResponse.data.needUpload) {
        // 文件已存在，直接返回URL
        updateTask(taskId, { 
          status: UploadStatus.COMPLETED,
          progress: 100,
          uploadedChunks: chunks.length,
          endTime: Date.now()
        });
        
        const completedTask = tasks.get(taskId)!;
        onSuccess?.(completedTask, initResponse.data.fileUrl!);
        return initResponse.data.fileUrl!;
      }

      // 更新任务信息
      updateTask(taskId, { 
        uploadId: initResponse.data.uploadId,
        uploadedChunks: initResponse.data.uploadedChunks.length
      });

      // 创建取消控制器
      const abortController = new AbortController();
      abortControllers.current.set(taskId, abortController);
      uploadingTasks.current.add(taskId);

      // 上传分片
      await uploadChunks(task, chunks);

      // 合并分片
      const mergeParams: ChunkMergeParams = {
        uploadId: initResponse.data.uploadId,
        fileMd5
      };

      const mergeResponse = await mergeChunks(mergeParams);
      
      if (mergeResponse.data.success) {
        updateTask(taskId, {
          status: UploadStatus.COMPLETED,
          progress: 100,
          endTime: Date.now()
        });

        const completedTask = tasks.get(taskId)!;
        onSuccess?.(completedTask, mergeResponse.data.fileUrl);
        return mergeResponse.data.fileUrl;
      } else {
        throw new Error(mergeResponse.data.message || '文件合并失败');
      }

    } catch (error: any) {
      const errorMessage = error.message || '上传失败';
      updateTask(taskId, { 
        status: UploadStatus.FAILED,
        error: errorMessage,
        endTime: Date.now()
      });

      const failedTask = tasks.get(taskId)!;
      onError?.(failedTask, errorMessage);
      throw error;
    } finally {
      uploadingTasks.current.delete(taskId);
      abortControllers.current.delete(taskId);
    }
  }, [options.chunkSize, tasks, updateTask, onStatusChange, onSuccess, onError, uploadChunks]);

  // 暂停上传
  const pauseUpload = useCallback((taskId: string) => {
    const abortController = abortControllers.current.get(taskId);
    if (abortController) {
      abortController.abort();
      updateTask(taskId, { status: UploadStatus.PAUSED });
    }
  }, [updateTask]);

  // 恢复上传
  const resumeUpload = useCallback(async (taskId: string) => {
    const task = tasks.get(taskId);
    if (task && task.status === UploadStatus.PAUSED) {
      // 重新开始上传流程
      try {
        await uploadFile(task.file);
      } catch (error) {
        console.error('Resume upload failed:', error);
      }
    }
  }, [tasks, uploadFile]);

  // 取消上传
  const cancelUpload = useCallback(async (taskId: string) => {
    const task = tasks.get(taskId);
    if (task) {
      // 取消网络请求
      const abortController = abortControllers.current.get(taskId);
      if (abortController) {
        abortController.abort();
      }

      // 如果有uploadId，调用取消接口
      if (task.uploadId) {
        try {
          await cancelChunkUpload(task.uploadId);
        } catch (error) {
          console.error('Cancel upload failed:', error);
        }
      }

      updateTask(taskId, { status: UploadStatus.CANCELLED });
      uploadingTasks.current.delete(taskId);
      abortControllers.current.delete(taskId);
    }
  }, [tasks, updateTask]);

  // 获取任务
  const getTask = useCallback((taskId: string) => {
    return tasks.get(taskId);
  }, [tasks]);

  // 获取所有任务
  const getAllTasks = useCallback(() => {
    return Array.from(tasks.values());
  }, [tasks]);

  // 清理已完成的任务
  const clearCompletedTasks = useCallback(() => {
    setTasks(prev => {
      const newTasks = new Map();
      for (const [id, task] of prev) {
        if (task.status !== UploadStatus.COMPLETED && task.status !== UploadStatus.FAILED) {
          newTasks.set(id, task);
        }
      }
      return newTasks;
    });
  }, []);

  return {
    uploadFile,
    pauseUpload,
    resumeUpload,
    cancelUpload,
    getTask,
    getAllTasks,
    clearCompletedTasks
  };
};

import { useRef, useState, useCallback, useEffect } from 'react';
import { getTrajectory } from '@/api/linban';
import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import Feature from 'ol/Feature';
import { Point, LineString } from 'ol/geom';
import { Style, Stroke, Fill, Circle as CircleStyle } from 'ol/style';
import { fromLonLat } from 'ol/proj';
import { message } from 'antd';
import type { Map as OlMap } from 'ol';

// 线性插值函数，用于平滑动画
const lerp = (start: number[], end: number[], t: number): number[] => {
  return [
    start[0] + (end[0] - start[0]) * t,
    start[1] + (end[1] - start[1]) * t
  ];
};

// 时间格式化函数
const formatTime = (date: Date): string => {
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 计算时间差（秒）
const getTimeDifference = (startTime: string, endTime: string): number => {
  const start = new Date(startTime).getTime();
  const end = new Date(endTime).getTime();
  return Math.abs(end - start) / 1000;
};

// 格式化时长（秒转为可读格式）
const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}时${minutes}分${secs}秒`;
  } else if (minutes > 0) {
    return `${minutes}分${secs}秒`;
  } else {
    return `${secs}秒`;
  }
};

export default function useTrajectoryPlayback(mapInstance: OlMap | null) {
  const animationFrameIdRef = useRef<number | null>(null);
  const coordsRef = useRef<number[][]>([]);
  const trajectoryDataRef = useRef<any[]>([]);
  const ptFeatRef = useRef<Feature<Point> | null>(null);
  const trajLayerRef = useRef<VectorLayer<any> | null>(null);
  const animLayerRef = useRef<VectorLayer<any> | null>(null);

  const [progress, setProgress] = useState(0);
  const [speed, _setSpeed] = useState(1);
  const [isPlaying, setIsPlaying] = useState(false);
  const [hasTrajectoryData, setHasTrajectoryData] = useState(false);
  const [isLoadingTrajectory, setIsLoadingTrajectory] = useState(false);
  const [currentTime, setCurrentTime] = useState<string>('');
  const [playbackTime, setPlaybackTime] = useState<string>('');

  // refs to hold current playing and speed values for animate
  const isPlayingRef = useRef(false);
  const speedRef = useRef(1);
  const playbackStartTimeRef = useRef(0);
  const pausedElapsedRef = useRef(0);
  const baseDuration = 15000; // 增加基础持续时间使动画更平滑

  // synchronize refs when state changes
  useEffect(() => { isPlayingRef.current = isPlaying; }, [isPlaying]);
  useEffect(() => { speedRef.current = speed; }, [speed]);

  const animate = useCallback((timestamp: number) => {
    if (!coordsRef.current.length || !trajectoryDataRef.current.length) return;
    if (!playbackStartTimeRef.current) {
      playbackStartTimeRef.current = timestamp - pausedElapsedRef.current;
    }
    
    const elapsed = (timestamp - playbackStartTimeRef.current) * speedRef.current;
    const frac = Math.min(elapsed / baseDuration, 1);
    setProgress(frac);
    
    // 计算当前播放时间
    const trajectoryData = trajectoryDataRef.current;
    if (trajectoryData.length > 0) {
      const totalPoints = trajectoryData.length;
      const exactIndex = frac * (totalPoints - 1);
      const currentIndex = Math.floor(exactIndex);
      const nextIndex = Math.min(currentIndex + 1, totalPoints - 1);
      
      // 获取当前时间点的数据
      const currentData = trajectoryData[currentIndex];
      const nextData = trajectoryData[nextIndex];
      
      if (currentData && currentData.createTime) {
        if (currentIndex === nextIndex) {
          // 最后一个点
          setCurrentTime(formatTime(new Date(currentData.createTime)));
        } else {
          // 在两个时间点之间插值
          const indexFraction = exactIndex - currentIndex;
          const currentTime = new Date(currentData.createTime).getTime();
          const nextTime = new Date(nextData.createTime).getTime();
          const interpolatedTime = currentTime + (nextTime - currentTime) * indexFraction;
          setCurrentTime(formatTime(new Date(interpolatedTime)));
        }
      }
      
                    // 计算播放时长（从动画开始到现在的时间，考虑倍速）
       if (playbackStartTimeRef.current > 0) {
         const now = performance.now();
         const elapsedAnimationTime = (now - playbackStartTimeRef.current) / 1000; // 动画已播放时间（秒）
         setPlaybackTime(formatDuration(elapsedAnimationTime));
       }
    }
    
    // 使用更精确的索引计算和插值
    const totalPoints = coordsRef.current.length;
    const exactIndex = frac * (totalPoints - 1);
    const lowerIndex = Math.floor(exactIndex);
    const upperIndex = Math.min(lowerIndex + 1, totalPoints - 1);
    const indexFraction = exactIndex - lowerIndex;
    
    let currentPos: number[];
    
    // 如果是最后一个点或者只有一个点，直接使用
    if (lowerIndex === upperIndex || totalPoints === 1) {
      currentPos = coordsRef.current[lowerIndex];
    } else {
      // 在两个点之间进行线性插值，实现平滑动画
      const startPos = coordsRef.current[lowerIndex];
      const endPos = coordsRef.current[upperIndex];
      currentPos = lerp(startPos, endPos, indexFraction);
    }
    
    const geom = ptFeatRef.current?.getGeometry();
    if (geom) {
      (geom as Point).setCoordinates(currentPos);
    }
    
    if (frac < 1 && isPlayingRef.current) {
      animationFrameIdRef.current = requestAnimationFrame(animate);
    } else if (frac >= 1) {
      // 播放完成，停止播放状态
      isPlayingRef.current = false;
      setIsPlaying(false);
    }
  }, []);

  // 查询轨迹数据（不自动播放）
  const queryTrajectory = useCallback(async (userId: number, startTime?: string, endTime?: string) => {
    if (!mapInstance) {
      console.error('地图实例不存在');
      return;
    }
    
    setIsLoadingTrajectory(true);
    try {
      const res = await getTrajectory(String(userId), startTime, endTime);
      const pts = res.data;
      
      if (!Array.isArray(pts) || pts.length === 0) {
        message.info('选择的时间段内没有轨迹数据');
        setHasTrajectoryData(false);
        return;
      }
      
      const coords = pts.map((p: any) => {
        const lonLat = [p.longitude, p.latitude];
        const transformed = fromLonLat(lonLat);
        return transformed;
      });
      
      coordsRef.current = coords;
      trajectoryDataRef.current = pts;
      
      // 设置初始时间
      if (pts.length > 0 && pts[0].createTime) {
        const startTime = new Date(pts[0].createTime);
        setCurrentTime(formatTime(startTime));
      }
      
      // remove existing layers
      if (trajLayerRef.current) { 
        mapInstance.removeLayer(trajLayerRef.current); 
      }
      if (animLayerRef.current) { 
        mapInstance.removeLayer(animLayerRef.current); 
      }
      
      // draw line
      const lineFeat = new Feature(new LineString(coords));
      const trajSource = new VectorSource({ features: [lineFeat] });
      const trajLayer = new VectorLayer({ 
        source: trajSource, 
        style: new Style({ 
          stroke: new Stroke({ 
            color: '#ff4d4f', 
            width: 4
          }) 
        }),
        zIndex: 1000 // 确保图层在顶层
      });
      
      mapInstance.addLayer(trajLayer);
      trajLayerRef.current = trajLayer;
      
      // setup animation point
      const ptFeat = new Feature(new Point(coords[0]));
      ptFeat.setStyle(new Style({ 
        image: new CircleStyle({ 
          radius: 8, 
          fill: new Fill({ color: '#1890ff' }), 
          stroke: new Stroke({ color: 'white', width: 2 }) 
        }),
        zIndex: 1001 // 确保动画点在轨迹线之上
      }));
      const animSource = new VectorSource({ features: [ptFeat] });
      const animLayer = new VectorLayer({ 
        source: animSource,
        zIndex: 1001
      });
      
      mapInstance.addLayer(animLayer);
      ptFeatRef.current = ptFeat;
      animLayerRef.current = animLayer;
      
      // 缩放地图到轨迹范围
      try {
        const extent = trajSource.getExtent();
        mapInstance.getView().fit(extent, {
          padding: [50, 50, 50, 50],
          duration: 1000,
          maxZoom: 16
        });
      } catch (fitError) {
        console.error('缩放地图失败:', fitError);
      }
      
      // reset progress
      setProgress(0);
      playbackStartTimeRef.current = 0;
      pausedElapsedRef.current = 0;
      setPlaybackTime('0秒');
      setHasTrajectoryData(true);
      
      message.success(`成功加载 ${pts.length} 个轨迹点`);
    } catch (err) {
      console.error('获取轨迹失败', err);
      message.error('获取轨迹数据失败');
      setHasTrajectoryData(false);
    } finally {
      setIsLoadingTrajectory(false);
    }
  }, [mapInstance]);

  // 保持原有的startTrajectory函数以兼容现有代码（查询24小时轨迹并自动播放）
  const startTrajectory = useCallback(async (userId: number) => {
    await queryTrajectory(userId);
    // 自动开始播放
    if (coordsRef.current.length > 0) {
      isPlayingRef.current = true;
      setIsPlaying(true);
      animationFrameIdRef.current = requestAnimationFrame(animate);
    }
  }, [queryTrajectory, animate]);

  const play = useCallback(() => {
    if (!coordsRef.current.length) {
      message.warning('请先查询轨迹数据');
      return;
    }
    
    // 如果播放已完成（progress >= 1），重新开始播放
    if (progress >= 1) {
      setProgress(0);
      playbackStartTimeRef.current = 0;
      pausedElapsedRef.current = 0;
      setPlaybackTime('0秒');
      
      // 重置动画点到起始位置
      if (ptFeatRef.current && coordsRef.current.length > 0) {
        const geom = ptFeatRef.current.getGeometry();
        if (geom) {
          (geom as Point).setCoordinates(coordsRef.current[0]);
        }
      }
      
      // 重置当前时间到起始时间
      if (trajectoryDataRef.current.length > 0 && trajectoryDataRef.current[0].createTime) {
        const startTime = new Date(trajectoryDataRef.current[0].createTime);
        setCurrentTime(formatTime(startTime));
      }
    }
    
    isPlayingRef.current = true;
    setIsPlaying(true);
    animationFrameIdRef.current = requestAnimationFrame(animate);
  }, [animate, progress]);

  const pause = useCallback(() => {
    isPlayingRef.current = false;
    setIsPlaying(false);
    if (animationFrameIdRef.current) {
      cancelAnimationFrame(animationFrameIdRef.current);
    }
  }, []);

  const seek = useCallback((newFrac: number) => {
    if (!coordsRef.current.length || !trajectoryDataRef.current.length) return;
    
    // 使用与动画相同的插值逻辑
    const totalPoints = coordsRef.current.length;
    const exactIndex = newFrac * (totalPoints - 1);
    const lowerIndex = Math.floor(exactIndex);
    const upperIndex = Math.min(lowerIndex + 1, totalPoints - 1);
    const indexFraction = exactIndex - lowerIndex;
    
    let pos: number[];
    
    if (lowerIndex === upperIndex || totalPoints === 1) {
      pos = coordsRef.current[lowerIndex];
    } else {
      const startPos = coordsRef.current[lowerIndex];
      const endPos = coordsRef.current[upperIndex];
      pos = lerp(startPos, endPos, indexFraction);
    }
    
    const geom = ptFeatRef.current?.getGeometry();
    if (geom) {
      (geom as Point).setCoordinates(pos);
    }
    
    // 更新时间显示
    const trajectoryData = trajectoryDataRef.current;
    if (trajectoryData.length > 0) {
      const currentIndex = Math.floor(exactIndex);
      const nextIndex = Math.min(currentIndex + 1, trajectoryData.length - 1);
      const currentData = trajectoryData[currentIndex];
      const nextData = trajectoryData[nextIndex];
      
      if (currentData && currentData.createTime) {
        if (currentIndex === nextIndex) {
          setCurrentTime(formatTime(new Date(currentData.createTime)));
        } else {
          const currentTime = new Date(currentData.createTime).getTime();
          const nextTime = new Date(nextData.createTime).getTime();
          const interpolatedTime = currentTime + (nextTime - currentTime) * indexFraction;
          setCurrentTime(formatTime(new Date(interpolatedTime)));
        }
      }
      
       // 更新播放时长（根据进度计算）
       const estimatedDuration = baseDuration / 1000; // 基础动画时长（秒）
       const currentPlaybackTime = estimatedDuration * newFrac;
       setPlaybackTime(formatDuration(currentPlaybackTime));
    }
    
    setProgress(newFrac);
    pausedElapsedRef.current = newFrac * baseDuration;
    playbackStartTimeRef.current = performance.now() - pausedElapsedRef.current;
  }, []);

  const setSpeed = useCallback((newSpeed: number) => {
    // 限制速度范围，确保合理的播放效果
    const clampedSpeed = Math.max(0.1, Math.min(100, newSpeed));
    const oldSpeed = speedRef.current;
    speedRef.current = clampedSpeed;
    _setSpeed(clampedSpeed);
    
    // 如果正在播放，需要重新计算时间基准点以保持平滑过渡
    if (isPlayingRef.current && playbackStartTimeRef.current > 0) {
      const now = performance.now();
      // 计算当前已播放的实际时间（考虑旧的速度）
      const currentElapsed = (now - playbackStartTimeRef.current) * oldSpeed;
      // 重新设置基准点，使新速度能够从当前进度继续播放
      playbackStartTimeRef.current = now - currentElapsed / clampedSpeed;
          }
    }, []);

  const clearTrajectory = useCallback(() => {
    if (trajLayerRef.current && mapInstance) {
      mapInstance.removeLayer(trajLayerRef.current);
      trajLayerRef.current = null;
    }
    if (animLayerRef.current && mapInstance) {
      mapInstance.removeLayer(animLayerRef.current);
      animLayerRef.current = null;
    }
    if (animationFrameIdRef.current) {
      cancelAnimationFrame(animationFrameIdRef.current);
      animationFrameIdRef.current = null;
    }
    setProgress(0);
    isPlayingRef.current = false;
    setIsPlaying(false);
    playbackStartTimeRef.current = 0;
    pausedElapsedRef.current = 0;
    setHasTrajectoryData(false);
    setCurrentTime('');
    setPlaybackTime('');
    coordsRef.current = [];
    trajectoryDataRef.current = [];
  }, [mapInstance]);

  return { 
    startTrajectory, 
    queryTrajectory, 
    play, 
    pause, 
    seek, 
    setSpeed, 
    progress, 
    speed, 
    isPlaying, 
    hasTrajectoryData, 
    isLoadingTrajectory,
    currentTime,
    playbackTime,
    clearTrajectory 
  };
} 
import { ChooseModalState } from "@/pages/common/ChooseModal";
import { CheckListItem } from "@/pages/common/ChooseModal/ChooseBox/CheckItem";
import mitt from "mitt";
import {
  GroupItem,
  MessageItem,
} from "@openim/wasm-client-sdk/lib/types/entity";
import { InviteData } from "@/pages/common/RtcCallModal/data";

type EmitterEvents = {
  OPEN_USER_CARD: OpenUserCardParams;
  OPEN_GROUP_CARD: GroupItem;
  OPEN_CHOOSE_MODAL: ChooseModalState;
  CHAT_LIST_SCROLL_TO_BOTTOM: void;
  OPEN_RTC_MODAL: InviteData;
  // message store
  PUSH_NEW_MSG: MessageItem;
  UPDATE_ONE_MSG: MessageItem;

  SELECT_USER: SelectUserParams;
  // 显示地图工具栏事件
  SHOW_MAP_TOOLBAR: ShowMapToolbarParams;
  // 绘制完成事件
  DRAW_COMPLETE: DrawCompleteParams;
  // 林班筛选事件
  FILTER_BY_LINBAN: FilterByLinbanParams;
  // 地图实例初始化完成事件
  MAP_INITIALIZED: any;
  // 测量模式状态事件
  MEASURE_MODE_CHANGED: MeasureModeParams;
  // 绘制模式状态事件
  DRAWING_MODE_CHANGED: DrawingModeParams;
};

export type SelectUserParams = {
  notConversation: boolean;
  choosedList: CheckListItem[];
};

export type OpenUserCardParams = {
  userID?: string;
  groupID?: string;
  isSelf?: boolean;
  notAdd?: boolean;
};

// 用于显示地图工具栏的事件类型
export interface ShowMapToolbarParams {
  type?: 'marker' | 'polygon' | 'linestring' | 'point';
  isAnnotation?: boolean;
}

// 绘制完成的事件类型
export interface DrawCompleteParams {
  feature: any;
  isAnnotation?: boolean;
}

// 林班筛选的事件类型
export interface FilterByLinbanParams {
  linbanId: number | null;
}

// 测量模式状态的事件类型
export interface MeasureModeParams {
  isActive: boolean;
  type?: 'distance' | 'area';
}

// 绘制模式状态的事件类型
export interface DrawingModeParams {
  isActive: boolean;
  type?: string;
}

const emitter = mitt<EmitterEvents>();

export const emit = emitter.emit;

export default emitter;

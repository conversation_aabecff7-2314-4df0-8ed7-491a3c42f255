import SparkMD5 from 'spark-md5';

/**
 * 计算文件MD5值
 */
export const calculateFileMD5 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const spark = new SparkMD5.ArrayBuffer();
    const fileReader = new FileReader();
    const chunkSize = 2097152; // 2MB chunks for MD5 calculation
    let currentChunk = 0;
    const chunks = Math.ceil(file.size / chunkSize);

    fileReader.onload = (e) => {
      if (e.target?.result) {
        spark.append(e.target.result as ArrayBuffer);
        currentChunk++;

        if (currentChunk < chunks) {
          loadNext();
        } else {
          resolve(spark.end());
        }
      }
    };

    fileReader.onerror = () => {
      reject(new Error('文件读取失败'));
    };

    const loadNext = () => {
      const start = currentChunk * chunkSize;
      const end = Math.min(start + chunkSize, file.size);
      fileReader.readAsArrayBuffer(file.slice(start, end));
    };

    loadNext();
  });
};

/**
 * 计算分片MD5值
 */
export const calculateChunkMD5 = (chunk: Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const spark = new SparkMD5.ArrayBuffer();
    const fileReader = new FileReader();

    fileReader.onload = (e) => {
      if (e.target?.result) {
        spark.append(e.target.result as ArrayBuffer);
        resolve(spark.end());
      }
    };

    fileReader.onerror = () => {
      reject(new Error('分片读取失败'));
    };

    fileReader.readAsArrayBuffer(chunk);
  });
};

/**
 * 将文件分割成分片
 */
export const createFileChunks = (file: File, chunkSize: number): Blob[] => {
  const chunks: Blob[] = [];
  let start = 0;

  while (start < file.size) {
    const end = Math.min(start + chunkSize, file.size);
    chunks.push(file.slice(start, end));
    start = end;
  }

  return chunks;
};

/**
 * 获取推荐的分片大小
 */
export const getRecommendedChunkSize = (fileSize: number): number => {
  if (fileSize < 10 * 1024 * 1024) { // < 10MB
    return 1024 * 1024; // 1MB
  } else if (fileSize < 100 * 1024 * 1024) { // < 100MB
    return 2 * 1024 * 1024; // 2MB
  } else if (fileSize < 1024 * 1024 * 1024) { // < 1GB
    return 5 * 1024 * 1024; // 5MB
  } else {
    return 10 * 1024 * 1024; // 10MB
  }
};

/**
 * 格式化文件大小
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 获取文件扩展名
 */
export const getFileExtension = (fileName: string): string => {
  const lastDotIndex = fileName.lastIndexOf('.');
  return lastDotIndex > 0 ? fileName.substring(lastDotIndex + 1).toLowerCase() : '';
};

/**
 * 验证文件类型
 */
export const validateFileType = (file: File, allowedTypes?: string[]): boolean => {
  if (!allowedTypes || allowedTypes.length === 0) {
    return true;
  }

  const fileExtension = getFileExtension(file.name);
  const mimeType = file.type;

  return allowedTypes.some(type => {
    // 支持扩展名匹配 (如 'jpg', 'png')
    if (type === fileExtension) {
      return true;
    }
    // 支持MIME类型匹配 (如 'image/*', 'video/mp4')
    if (type.includes('/')) {
      if (type.endsWith('/*')) {
        return mimeType.startsWith(type.replace('/*', '/'));
      }
      return mimeType === type;
    }
    return false;
  });
};

/**
 * 验证文件大小
 */
export const validateFileSize = (file: File, maxSize?: number): boolean => {
  if (!maxSize) {
    return true;
  }
  return file.size <= maxSize;
};

/**
 * 上传状态枚举
 */
export enum UploadStatus {
  PENDING = 'pending',
  UPLOADING = 'uploading',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

/**
 * 上传任务接口
 */
export interface UploadTask {
  id: string;
  file: File;
  uploadId?: string;
  status: UploadStatus;
  progress: number;
  uploadedChunks: number;
  totalChunks: number;
  chunkSize: number;
  fileMd5?: string;
  error?: string;
  startTime?: number;
  endTime?: number;
  speed?: number; // 上传速度 (bytes/second)
}

/**
 * 计算上传速度
 */
export const calculateUploadSpeed = (uploadedBytes: number, startTime: number): number => {
  const elapsedTime = (Date.now() - startTime) / 1000; // 秒
  return elapsedTime > 0 ? uploadedBytes / elapsedTime : 0;
};

/**
 * 格式化上传速度
 */
export const formatUploadSpeed = (bytesPerSecond: number): string => {
  return formatFileSize(bytesPerSecond) + '/s';
};

/**
 * 估算剩余时间
 */
export const estimateRemainingTime = (
  totalBytes: number,
  uploadedBytes: number,
  speed: number
): number => {
  if (speed <= 0) return Infinity;
  const remainingBytes = totalBytes - uploadedBytes;
  return remainingBytes / speed; // 秒
};

/**
 * 格式化剩余时间
 */
export const formatRemainingTime = (seconds: number): string => {
  if (seconds === Infinity || isNaN(seconds)) {
    return '--:--';
  }

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
};

/**
 * 重试配置
 */
export interface RetryConfig {
  maxRetries: number;
  retryDelay: number; // 毫秒
  backoffMultiplier: number; // 退避倍数
}

/**
 * 默认重试配置
 */
export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  retryDelay: 1000,
  backoffMultiplier: 2
};

/**
 * 延迟函数
 */
export const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * 带重试的异步函数执行
 */
export const withRetry = async <T>(
  fn: () => Promise<T>,
  config: RetryConfig = DEFAULT_RETRY_CONFIG
): Promise<T> => {
  let lastError: Error;
  let currentDelay = config.retryDelay;

  for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === config.maxRetries) {
        break;
      }

      await delay(currentDelay);
      currentDelay *= config.backoffMultiplier;
    }
  }

  throw lastError!;
};

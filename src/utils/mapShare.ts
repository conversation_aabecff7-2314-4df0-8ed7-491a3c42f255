import { IMSDK } from '@/layout/MainContentWrap';
import { message } from 'antd';
import { toLonLat } from 'ol/proj';
import { SendMsgParams } from "@openim/wasm-client-sdk/lib/types/params";

/**
 * 创建位置消息
 * @param labelInfo 标注信息
 * @param coordinate 坐标 [经度, 纬度]
 * @returns 位置消息对象
 */
export const createLocationMessage = async (labelInfo: any, coordinate: number[]) => {
  try {
    // 获取标注的经纬度坐标
    const [longitude, latitude] = coordinate;
    
    // 创建自定义消息
    // 因为OpenIM可能没有直接的位置消息类型，所以使用自定义消息实现
    const { data: message } = await IMSDK.createCustomMessage({
      data: JSON.stringify({
        type: 'location',
        labelId: labelInfo.labelId || '',
        labelName: labelInfo.name || labelInfo.labelName || '未命名标记',
        description: labelInfo.remark || labelInfo.description || '',
        longitude,
        latitude,
        // 可以添加更多标记信息
        timestamp: new Date().getTime()
      }),
      extension: '林班地图标记',
      description: `[位置] ${labelInfo.name || labelInfo.labelName || '未命名标记'}`,
    });
    
    return message;
  } catch (error) {
    console.error('创建位置消息失败:', error);
    throw error;
  }
};

/**
 * 发送位置消息给指定用户
 * @param users 接收者列表
 * @param feature 标注特征对象
 * @param labelInfo 标注信息
 * @returns 发送结果
 */
export const sendLocationMessageToUsers = async (users: any[], feature: any, labelInfo: any) => {
  if (!users || users.length === 0 || !feature) {
    return { success: false, message: '发送失败：缺少必要参数' };
  }

  try {
    // 获取几何中心点
    let coordinate;
    const geometry = feature.getGeometry();
    if (geometry) {
      // 获取几何中心点坐标，并转换为经纬度
      const extent = geometry.getExtent();
      const center = [
        (extent[0] + extent[2]) / 2,
        (extent[1] + extent[3]) / 2
      ];
      coordinate = toLonLat(center);
    } else if (labelInfo && labelInfo.dataJson) {
      // 尝试从labelInfo中解析坐标
      try {
        const dataJson = typeof labelInfo.dataJson === 'string' 
          ? JSON.parse(labelInfo.dataJson) 
          : labelInfo.dataJson;
        
        if (dataJson.geometry && dataJson.geometry.coordinates) {
          coordinate = dataJson.geometry.coordinates;
        }
      } catch (e) {
        console.error('解析标注JSON失败:', e);
      }
    }

    if (!coordinate) {
      return { success: false, message: '发送失败：无法获取标注坐标' };
    }

    // 创建位置消息
    const locationMessage = await createLocationMessage(labelInfo, coordinate);
    
    // 向每个选中的用户发送位置消息
    const sendPromises = users.map(async (user) => {
      try {
        // 发送单聊消息
        const sendParams: SendMsgParams = {
          recvID: user.userID,
          groupID: "", // 单聊时设为空字符串
          message: locationMessage,
          isOnlineOnly: false,
        };
        
        await IMSDK.sendMessage(sendParams);
        return { success: true, userID: user.userID };
      } catch (error) {
        console.error(`向用户 ${user.userID} 发送消息失败:`, error);
        return { success: false, userID: user.userID, error };
      }
    });

    const results = await Promise.all(sendPromises);
    const successCount = results.filter(r => r.success).length;
    
    if (successCount === 0) {
      return { success: false, message: '发送失败：所有消息发送失败' };
    } else if (successCount < users.length) {
      return { 
        success: true, 
        message: `部分发送成功：${successCount}/${users.length}` 
      };
    } else {
      return { success: true, message: '发送成功' };
    }
  } catch (error) {
    console.error('发送位置消息失败:', error);
    return { success: false, message: '发送位置消息失败' };
  }
}; 
import React from 'react';
import { Card, Space, Typography, Divider } from 'antd';
import FileUploader from '@/pages/data/components/FileUploader';

const { Title, Text } = Typography;

const FileUploadTest: React.FC = () => {
  const handleUploadProgress = (fileId: string, progress: number) => {
    console.log(`文件 ${fileId} 上传进度: ${progress}%`);
  };

  const handleUploadComplete = (fileId: string, filePath: string) => {
    console.log(`文件 ${fileId} 上传完成: ${filePath}`);
  };

  const handleUploadError = (fileId: string, error: string) => {
    console.error(`文件 ${fileId} 上传失败: ${error}`);
  };

  return (
    <div style={{ padding: 24, maxWidth: 800, margin: '0 auto' }}>
      <Title level={2}>文件上传测试</Title>
      <Text type="secondary">
        测试新的分片上传功能，支持大文件上传、断点续传、并发控制等特性。
      </Text>

      <Divider />

      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Card title="普通文件上传测试" size="small">
          <FileUploader
            collectDate="2024-01-01"
            linbanId="1"
            linbanName="测试林班"
            collector="测试用户"
            remark="测试上传功能"
            enableChunkUpload={false}
            maxFileSize={50 * 1024 * 1024} // 50MB
            onUploadProgress={handleUploadProgress}
            onUploadComplete={handleUploadComplete}
            onUploadError={handleUploadError}
          />
        </Card>

        <Card title="分片上传测试" size="small">
          <FileUploader
            collectDate="2024-01-01"
            linbanId="1"
            linbanName="测试林班"
            collector="测试用户"
            remark="测试分片上传功能"
            enableChunkUpload={true}
            chunkUploadThreshold={5 * 1024 * 1024} // 5MB启用分片上传
            maxFileSize={500 * 1024 * 1024} // 500MB
            onUploadProgress={handleUploadProgress}
            onUploadComplete={handleUploadComplete}
            onUploadError={handleUploadError}
          />
        </Card>

        <Card title="限制文件类型测试" size="small">
          <FileUploader
            collectDate="2024-01-01"
            linbanId="1"
            linbanName="测试林班"
            collector="测试用户"
            remark="测试文件类型限制"
            enableChunkUpload={true}
            allowedTypes={['jpg', 'png', 'pdf', 'geojson', 'tif', 'shp']}
            maxFileSize={100 * 1024 * 1024} // 100MB
            onUploadProgress={handleUploadProgress}
            onUploadComplete={handleUploadComplete}
            onUploadError={handleUploadError}
          />
        </Card>

        <Card title="MIME类型限制测试" size="small">
          <FileUploader
            collectDate="2024-01-01"
            linbanId="1"
            linbanName="测试林班"
            collector="测试用户"
            remark="测试MIME类型限制"
            enableChunkUpload={true}
            allowedTypes={['image/*', 'application/pdf', 'application/json']}
            maxFileSize={100 * 1024 * 1024} // 100MB
            onUploadProgress={handleUploadProgress}
            onUploadComplete={handleUploadComplete}
            onUploadError={handleUploadError}
          />
        </Card>
      </Space>

      <Divider />

      <Card title="功能说明" size="small">
        <Space direction="vertical">
          <Text strong>新功能特性：</Text>
          <ul style={{ paddingLeft: 20 }}>
            <li>✅ 大文件分片上传</li>
            <li>✅ 断点续传支持</li>
            <li>✅ 并发上传控制</li>
            <li>✅ 文件MD5校验</li>
            <li>✅ 重复文件检测</li>
            <li>✅ 上传进度显示</li>
            <li>✅ 上传速度计算</li>
            <li>✅ 剩余时间估算</li>
            <li>✅ 暂停/恢复上传</li>
            <li>✅ 取消上传</li>
            <li>✅ 文件类型验证</li>
            <li>✅ 文件大小验证</li>
            <li>✅ 错误重试机制</li>
          </ul>
          
          <Text strong>API接口：</Text>
          <ul style={{ paddingLeft: 20 }}>
            <li>POST /infra/file/chunk/init - 初始化分片上传</li>
            <li>POST /infra/file/chunk/upload - 上传文件分片</li>
            <li>POST /infra/file/chunk/merge - 合并文件分片</li>
            <li>DELETE /infra/file/chunk/cancel - 取消分片上传</li>
            <li>GET /infra/file/chunk/progress - 获取上传进度</li>
          </ul>
        </Space>
      </Card>
    </div>
  );
};

export default FileUploadTest;

import { FC } from "react";
import { <PERSON><PERSON>, <PERSON> } from "antd";
import { EnvironmentOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { MessageType } from "@openim/wasm-client-sdk";
import { IMessageItemProps } from ".";
import styles from "./message-item.module.scss";

const LocationMessageRender: FC<IMessageItemProps> = ({ message, isSender }) => {
  const navigate = useNavigate();
  
  // 尝试解析自定义消息
  let locationData = null;
  try {
    if (message.contentType === MessageType.CustomMessage && message.customElem) {
      const customData = JSON.parse(message.customElem.data);
      // 检查是否是位置消息类型
      if (customData.type === 'location') {
        locationData = customData;
      }
    }
  } catch (e) {
    console.error('解析位置消息失败:', e);
  }

  if (!locationData) {
    return <div className="p-2 rounded bg-gray-100">无法显示的位置消息</div>;
  }

  // 处理点击查看位置
  const handleViewLocation = () => {
    // 使用路由导航到地图页面，并传递位置参数
    navigate(`/home?lat=${locationData.latitude}&lng=${locationData.longitude}&labelId=${locationData.labelId || ''}&labelName=${encodeURIComponent(locationData.labelName || '位置标记')}`);
  };

  return (
    <Card 
      size="small" 
      className={`${styles["location-message"]} ${isSender ? styles["location-message-sender"] : ""}`}
      style={{ 
        width: 240,
        margin: 0
      }}
      bodyStyle={{ padding: '8px' }}
    >
      <div className="flex items-center">
        <EnvironmentOutlined style={{ fontSize: '18px', color: '#1890ff', marginRight: '8px' }} />
        <div className="flex-1 overflow-hidden">
          <div className="font-medium text-ellipsis overflow-hidden whitespace-nowrap">
            {locationData.labelName || '位置标记'}
          </div>
          {locationData.description && (
            <div className="text-xs text-gray-500 mt-1 text-ellipsis overflow-hidden whitespace-nowrap">
              {locationData.description}
            </div>
          )}
          <div className="text-xs text-gray-400 mt-1">
            {locationData.latitude.toFixed(6)}, {locationData.longitude.toFixed(6)}
          </div>
        </div>
      </div>
      <Button 
        type="primary" 
        size="small" 
        onClick={handleViewLocation} 
        style={{ 
          marginTop: 8,
          width: '100%'
        }}
      >
        查看位置
      </Button>
    </Card>
  );
};

export default LocationMessageRender; 
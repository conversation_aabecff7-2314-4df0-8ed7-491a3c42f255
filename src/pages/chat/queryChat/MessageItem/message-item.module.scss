.message-container {
  @apply flex flex-1 overflow-hidden;

  .message-wrap {
    @apply ml-3 flex flex-1 flex-col overflow-hidden;

    .message-profile {
      @apply mb-1 flex w-full text-xs;
    }

    .bubble {
      @apply w-fit rounded-md p-2.5;
      word-break: break-all;
      background-color: var(--chat-bubble);
    }

    .suffix {
      @apply ml-3 flex items-center;
    }
  }

  .menu-wrap {
    @apply flex w-fit;
  }

  &-sender {
    @apply flex-row-reverse;

    .message-wrap {
      @apply mr-3 items-end;

      .message-profile {
        @apply flex-row-reverse;
      }

      .bubble {
        background-color: var(--chat-bubble-sender);
      }

      .suffix {
        @apply ml-0 mr-3;
      }
    }

    .menu-wrap {
      @apply flex-row-reverse;
    }
  }
}

.animate-container {
  // background-color: var(--primary-active);
  animation: animate 2s ease-in-out;
}
@keyframes animate {
  from {
    background-color: var(--primary-active);
  }
  to {
    background-color: transparent;
  }
}

.card-shadow {
  @apply w-60 cursor-pointer overflow-hidden rounded-md shadow-md;
  box-shadow: 3px 3px 8px 1px rgba(81, 94, 112, 0.1);
}

.bubble {
  word-wrap: break-word;
  word-break: break-word;
  white-space: pre-wrap;
}

/* 位置消息样式 */
.location-message {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
  background-color: #fff;
  
  &-sender {
    background-color: #e1f6ff;
  }
}

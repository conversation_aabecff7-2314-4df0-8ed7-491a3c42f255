import React, { useState, useEffect } from 'react';
import { Button, Layout, Table, Modal, Form, DatePicker, Input, message, Tag, Space, Tooltip, Dropdown, Select } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, MoreOutlined, DownloadOutlined, EyeOutlined, ReloadOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import FileUploader from './components/FileUploader';
// @ts-ignore
import styles from './DataManagement.module.scss';
import { 
  getFileList, 
  deleteFile, 
  downloadFile, 
  FileInfo, 
  FileQueryParams 
} from '../../api/fileManagement';

const { Content } = Layout;

export const DataManagement = () => {
  const [dataList, setDataList] = useState<FileInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [isUploadModalVisible, setIsUploadModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();

  // 获取文件列表
  const fetchFileList = async (params?: Partial<FileQueryParams>) => {
    try {
      setLoading(true);
      const response = await getFileList({
        pageNo: currentPage,
        pageSize,
        ...params,
      });
      
      setDataList(response.data.list);
      setTotal(response.data.total);
    } catch (error: any) {
      console.error('获取文件列表失败:', error);
      message.error('获取文件列表失败: ' + (error?.msg || error.message || '网络错误'));
      
      // 如果API调用失败，使用空数据
      setDataList([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // 初始加载数据
  useEffect(() => {
    fetchFileList();
  }, [currentPage]);

  // 搜索文件
  const handleSearch = () => {
    const values = searchForm.getFieldsValue();
    const params: Partial<FileQueryParams> = {};
    
    if (values.fileName) params.originalName = values.fileName;
    if (values.fileType) params.fileType = values.fileType;
    if (values.linbanName) params.linbanName = values.linbanName;
    if (values.collector) params.collector = values.collector;
    if (values.collectDateRange && values.collectDateRange.length === 2) {
      params.collectDate = [
        values.collectDateRange[0].format('YYYY-MM-DD'),
        values.collectDateRange[1].format('YYYY-MM-DD')
      ];
    }
    
    setCurrentPage(1); // 重置到第一页
    fetchFileList(params);
  };

  // 重置搜索
  const handleReset = () => {
    searchForm.resetFields();
    setCurrentPage(1);
    fetchFileList();
  };

  // 获取文件图标
  const getFileIcon = (fileType?: string) => {
    switch (fileType?.toLowerCase()) {
      case 'geojson':
      case 'json':
        return '🗺️';
      case 'tif':
      case 'tiff':
        return '🖼️';
      case 'shp':
        return '📍';
      case 'pdf':
        return '📄';
      case 'jpg':
      case 'jpeg':
      case 'png':
        return '🖼️';
      default:
        return '📁';
    }
  };

  // 获取文件类型标签
  const getFileTypeTag = (fileType?: string) => {
    const colorMap: Record<string, string> = {
      geojson: 'blue',
      tif: 'green', 
      tiff: 'green',
      shp: 'orange',
      pdf: 'red',
      jpg: 'purple',
      jpeg: 'purple',
      png: 'purple',
    };
    
    const lowerType = fileType?.toLowerCase() || '';
    return (
      <Tag color={colorMap[lowerType] || 'default'}>
        {fileType?.toUpperCase() || 'UNKNOWN'}
      </Tag>
    );
  };

  // 下载文件
  const handleDownload = async (record: FileInfo) => {
    try {
      const response = await downloadFile(record.id);
      
      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', record.originalName || record.fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      message.success('文件下载成功');
    } catch (error: any) {
      message.error('文件下载失败: ' + (error.response?.data?.msg || error.message));
    }
  };

  // 删除文件
  const handleDelete = async (record: FileInfo) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除文件 "${record.originalName || record.fileName}" 吗？`,
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          await deleteFile(record.id);
          message.success('文件删除成功');
          fetchFileList(); // 重新加载列表
        } catch (error: any) {
          message.error('文件删除失败: ' + (error.response?.data?.msg || error.message));
        }
      },
    });
  };

  // 获取操作菜单项
  const getActionMenuItems = (record: FileInfo) => [
    {
      key: 'download',
      icon: <DownloadOutlined />,
      label: '下载',
      onClick: () => handleDownload(record),
    },
    {
      key: 'view',
      icon: <EyeOutlined />,
      label: '预览',
      onClick: () => {
        message.info('预览功能开发中');
      },
    },
    {
      key: 'edit',
      icon: <EditOutlined />,
      label: '编辑信息',
      onClick: () => {
        message.info('编辑功能开发中');
      },
    },
    {
      key: 'delete',
      icon: <DeleteOutlined />,
      label: '删除',
      danger: true,
      onClick: () => handleDelete(record),
    },
  ];

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 表格列定义
  const columns = [
    {
      title: '序号',
      dataIndex: 'id',
      key: 'id',
      width: 60,
      align: 'center' as const,
    },
    {
      title: '文件信息',
      key: 'fileInfo',
      width: 300,
      render: (text: any, record: FileInfo) => (
        <div className={styles['file-info-card']}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
            <span style={{ marginRight: '8px', fontSize: '18px' }}>{getFileIcon(record.fileType || record.fileExtension)}</span>
            <span style={{ fontWeight: '500', marginRight: '8px' }}>{record.originalName || record.fileName}</span>
            {getFileTypeTag(record.fileType || record.fileExtension)}
          </div>
          <div style={{ fontSize: '12px', color: '#666', display: 'flex', gap: '12px' }}>
            <span>大小: {formatFileSize(record.fileSize)}</span>
            <span>创建: {record.createTime}</span>
          </div>
        </div>
      ),
    },
    {
      title: '采集信息',
      key: 'collectInfo',
      width: 200,
      render: (text: any, record: FileInfo) => (
        <div style={{ fontSize: '13px', lineHeight: '1.4' }}>
          <div style={{ marginBottom: '2px' }}>采集时间: {record.collectDate}</div>
          <div style={{ marginBottom: '2px' }}>所属林班: {record.linbanName}</div>
          <div>采集人员: {record.collector}</div>
        </div>
      ),
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      align: 'center' as const,
      render: (text: any, record: FileInfo) => {
        const statusMap: Record<number, { color: string; text: string }> = {
          0: { color: 'processing', text: '上传中' },
          1: { color: 'success', text: '已上传' },
          2: { color: 'error', text: '上传失败' },
        };
        
        const status = statusMap[record.uploadStatus] || { color: 'default', text: '未知状态' };
        
        return <Tag color={status.color}>{status.text}</Tag>;
      },
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 150,
      ellipsis: true,
      render: (text: string) => (
        <Tooltip title={text}>
          {text || '-'}
        </Tooltip>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      align: 'center' as const,
      render: (text: any, record: FileInfo) => (
        <div className={styles['action-dropdown']}>
          <Dropdown
            menu={{
              items: getActionMenuItems(record),
            }}
            trigger={['click']}
          >
            <Button
              type="text"
              icon={<MoreOutlined />}
              size="small"
            />
          </Dropdown>
        </div>
      ),
    },
  ];

  // 上传进度处理
  const handleUploadProgress = (fileId: string, progress: number) => {
    console.log(`File ${fileId} progress: ${progress}%`);
  };

  // 上传完成处理
  const handleUploadComplete = (fileId: string, filePath: string) => {
    message.success(`文件 ${fileId} 上传成功`);
    fetchFileList(); // 重新加载文件列表
    setIsUploadModalVisible(false); // 关闭上传弹窗
    form.resetFields(); // 重置表单
  };

  // 上传错误处理
  const handleUploadError = (fileId: string, error: string) => {
    message.error(`文件 ${fileId} 上传失败: ${error}`);
  };

  // 关闭上传弹窗
  const handleCloseModal = () => {
    setIsUploadModalVisible(false);
    form.resetFields();
  };

  return (
    <Layout style={{ height: '100vh', background: '#f5f5f5' }}>
      <Content style={{ padding: '16px', display: 'flex', flexDirection: 'column' }}>
        {/* 页面头部 */}
        <div className={styles['data-management-header']}>
          <div className={styles['header-content']}>
            <div className={styles['title-section']}>
              <h2>数据管理</h2>
              <div className={styles.subtitle}>林班文件管理系统</div>
            </div>
            <div className={styles['action-section']}>
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => {
                  console.log('点击了上传文件按钮');
                  setIsUploadModalVisible(true);
                }}
                style={{ 
                  height: '40px',
                  fontSize: '14px',
                  fontWeight: '600',
                  background: 'rgba(255, 255, 255, 0.2)',
                  border: '2px solid rgba(255, 255, 255, 0.3)',
                  color: 'white'
                }}
              >
                上传文件
              </Button>
            </div>
          </div>
        </div>

        {/* 搜索表单 */}
        <div style={{ 
          background: 'white', 
          padding: '16px', 
          borderRadius: '8px', 
          marginBottom: '16px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <Form
            form={searchForm}
            layout="inline"
            onFinish={handleSearch}
          >
            <Form.Item name="fileName">
              <Input placeholder="文件名" style={{ width: 150 }} />
            </Form.Item>
            <Form.Item name="fileType">
              <Select placeholder="文件类型" style={{ width: 120 }} allowClear>
                <Select.Option value="geojson">GeoJSON</Select.Option>
                <Select.Option value="tif">TIF</Select.Option>
                <Select.Option value="shp">SHP</Select.Option>
                <Select.Option value="pdf">PDF</Select.Option>
                <Select.Option value="jpg">JPG</Select.Option>
                <Select.Option value="png">PNG</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item name="linbanName">
              <Input placeholder="林班名称" style={{ width: 120 }} />
            </Form.Item>
            <Form.Item name="collector">
              <Input placeholder="采集人员" style={{ width: 120 }} />
            </Form.Item>
            <Form.Item name="collectDateRange">
              <DatePicker.RangePicker 
                placeholder={['开始日期', '结束日期']}
                style={{ width: 240 }}
              />
            </Form.Item>
            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit">
                  搜索
                </Button>
                <Button onClick={handleReset}>
                  重置
                </Button>
                <Button 
                  icon={<ReloadOutlined />}
                  onClick={() => fetchFileList()}
                >
                  刷新
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </div>

        {/* 数据表格 */}
        <div className={styles['table-container']}>
          <Table
            className={styles['custom-table']}
            columns={columns}
            dataSource={dataList}
            rowKey="id"
            loading={loading}
            pagination={{
              current: currentPage,
              pageSize,
              total,
              showQuickJumper: true,
              showSizeChanger: false,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              onChange: (page) => setCurrentPage(page),
            }}
            size="small"
            scroll={{ y: 'calc(100vh - 300px)' }}
            locale={{
              emptyText: '暂无文件数据'
            }}
          />
        </div>
      </Content>

      {/* 上传弹窗 */}
      <Modal
        title="文件上传"
        open={isUploadModalVisible}
        onCancel={handleCloseModal}
        footer={null}
        width={600}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            collectDate: dayjs(),
          }}
        >
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <Form.Item
              label="采集时间"
              name="collectDate"
              rules={[{ required: true, message: '请选择采集时间' }]}
            >
              <DatePicker style={{ width: '100%' }} />
            </Form.Item>
            
            <Form.Item
              label="所属林班ID"
              name="linbanId"
              rules={[{ required: true, message: '请输入所属林班ID' }]}
            >
              <Input placeholder="请输入林班ID" />
            </Form.Item>

            <Form.Item
              label="所属林班名称"
              name="linbanName"
              rules={[{ required: true, message: '请输入所属林班名称' }]}
            >
              <Input placeholder="请输入林班名称" />
            </Form.Item>

            <Form.Item
              label="采集人员"
              name="collector"
              rules={[{ required: true, message: '请输入采集人员' }]}
            >
              <Input placeholder="请输入采集人员姓名" />
            </Form.Item>
          </div>

          <Form.Item
            label="备注"
            name="remark"
          >
            <Input.TextArea 
              placeholder="请输入备注信息（可选）" 
              rows={3}
            />
          </Form.Item>

          <Form.Item label="选择文件">
            <Form.Item dependencies={['collectDate', 'linbanId', 'linbanName', 'collector', 'remark']}>
              {({ getFieldsValue }) => {
                const values = getFieldsValue();
                const isFormValid = values.collectDate && values.linbanId && values.linbanName && values.collector;
                
                if (isFormValid) {
                  return (
                    <div>
                      <div style={{ marginBottom: 8, fontSize: '12px', color: '#666' }}>
                        ✅ 表单信息完整，可以开始上传文件
                      </div>
                      <FileUploader
                        collectDate={values.collectDate.format('YYYY-MM-DD')}
                        linbanId={values.linbanId}
                        linbanName={values.linbanName}
                        collector={values.collector}
                        remark={values.remark}
                        onUploadProgress={handleUploadProgress}
                        onUploadComplete={handleUploadComplete}
                        onUploadError={handleUploadError}
                      />
                    </div>
                  );
                } else {
                  const missingFields = [];
                  if (!values.collectDate) missingFields.push('采集时间');
                  if (!values.linbanId) missingFields.push('林班ID');
                  if (!values.linbanName) missingFields.push('林班名称');
                  if (!values.collector) missingFields.push('采集人员');
                  
                  return (
                    <div style={{ 
                      padding: 16, 
                      textAlign: 'center', 
                      color: '#999',
                      background: '#f9f9f9',
                      border: '1px dashed #d9d9d9',
                      borderRadius: '6px'
                    }}>
                      <div>请先填写完整的表单信息</div>
                      <div style={{ fontSize: '12px', marginTop: 4 }}>
                        缺少: {missingFields.join('、')}
                      </div>
                    </div>
                  );
                }
              }}
            </Form.Item>
          </Form.Item>

          <Form.Item style={{ marginTop: 24, marginBottom: 0 }}>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button onClick={handleCloseModal}>
                取消
              </Button>
              <Button type="primary" onClick={handleCloseModal}>
                确定
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </Layout>
  );
}; 
import { useState } from 'react';
import { Upload, Progress, Button, message } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { UploadFile } from 'antd/es/upload/interface';
import { 
  uploadFile as apiUploadFile,
  FileUploadParams
} from '@/api/fileManagement';

interface UploadingFile {
  file: File;
  uid: string;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
}

interface FileUploaderProps {
  collectDate: string;
  linbanId: string;
  linbanName: string;
  collector: string;
  remark?: string;
  onUploadProgress?: (fileId: string, progress: number) => void;
  onUploadComplete?: (fileId: string, filePath: string) => void;
  onUploadError?: (fileId: string, error: string) => void;
}

const FileUploader: React.FC<FileUploaderProps> = ({
  collectDate,
  linbanId,
  linbanName,
  collector,
  remark,
  onUploadProgress,
  onUploadComplete,
  onUploadError,
}) => {
  const [uploadingFiles, setUploadingFiles] = useState<Map<string, UploadingFile>>(new Map());
  const [fileList, setFileList] = useState<UploadFile[]>([]);

  // 处理文件上传
  const handleUpload = async (file: File) => {
    const uploadingFile: UploadingFile = {
      file,
      uid: file.name,
      progress: 0,
      status: 'uploading',
    };

    setUploadingFiles(prev => new Map(prev.set(file.name, uploadingFile)));

    try {
      const params: FileUploadParams = {
        file,
        linbanId: parseInt(linbanId),
        linbanName,
        collector,
        collectDate,
        description: remark,
      };

      // 模拟上传进度
      const progressTimer = setInterval(() => {
        setUploadingFiles(prev => {
          const newMap = new Map(prev);
          const currentFile = newMap.get(file.name);
          if (currentFile && currentFile.progress < 90) {
            currentFile.progress += 10;
            onUploadProgress?.(file.name, currentFile.progress);
          }
          return newMap;
        });
      }, 200);

      const response = await apiUploadFile(params);
      
      clearInterval(progressTimer);
      
      // 完成上传
      setUploadingFiles(prev => {
        const newMap = new Map(prev);
        const currentFile = newMap.get(file.name);
        if (currentFile) {
          currentFile.progress = 100;
          currentFile.status = 'completed';
          onUploadProgress?.(file.name, 100);
        }
        return newMap;
      });

      onUploadComplete?.(file.name, `文件上传成功，文件ID: ${response.data}`);
      message.success('文件上传成功！');
    } catch (error: any) {
      const errorMsg = error.response?.data?.msg || error.message || '上传失败';
      
      setUploadingFiles(prev => {
        const newMap = new Map(prev);
        const currentFile = newMap.get(file.name);
        if (currentFile) {
          currentFile.status = 'error';
        }
        return newMap;
      });

      onUploadError?.(file.name, errorMsg);
      message.error(`文件上传失败: ${errorMsg}`);
    }
    
    return false; // 阻止默认上传行为
  };

  // 移除文件
  const handleRemove = (file: UploadFile) => {
    setUploadingFiles(prev => {
      const newMap = new Map(prev);
      newMap.delete(file.uid);
      return newMap;
    });
    
    setFileList(prev => prev.filter(item => item.uid !== file.uid));
  };

  return (
    <div>
      <Upload
        multiple
        beforeUpload={handleUpload}
        onRemove={handleRemove}
        fileList={fileList}
        itemRender={(originNode, file) => {
          const uploadingFile = uploadingFiles.get(file.uid);
          
          if (!uploadingFile) {
            return originNode;
          }
          
          return (
            <div style={{ 
              border: '1px solid #d9d9d9', 
              borderRadius: '6px', 
              padding: '12px',
              marginBottom: '8px',
              background: '#fafafa'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                <span style={{ fontWeight: 500 }}>{uploadingFile.file.name}</span>
                <Button
                  size="small"
                  danger
                  onClick={() => handleRemove(file)}
                >
                  删除
                </Button>
              </div>
              
              <Progress
                percent={Math.round(uploadingFile.progress)}
                status={uploadingFile.status === 'error' ? 'exception' : 
                       uploadingFile.status === 'completed' ? 'success' : 'active'}
                size="small"
              />
              
              <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                {uploadingFile.status === 'completed' && '上传完成'}
                {uploadingFile.status === 'error' && '上传失败'}
                {uploadingFile.status === 'uploading' && '上传中...'}
              </div>
            </div>
          );
        }}
      >
        <Button icon={<UploadOutlined />} block>
          选择文件上传
        </Button>
      </Upload>
      
      <div style={{ marginTop: '12px', fontSize: '12px', color: '#666' }}>
        <div>• 支持批量上传多个文件</div>
        <div>• 推荐文件格式：GeoJSON、TIF、SHP、PDF、图片等</div>
        <div>• 单个文件建议不超过100MB</div>
      </div>
    </div>
  );
};

export default FileUploader; 
import { useState, useCallback } from 'react';
import { Upload, Progress, Button, message, Card, Space, Typography, Tag } from 'antd';
import { UploadOutlined, PauseOutlined, PlayCircleOutlined, CloseOutlined } from '@ant-design/icons';
import { UploadFile } from 'antd/es/upload/interface';
import {
  uploadFile as apiUploadFile,
  FileUploadParams
} from '@/api/fileManagement';
import { useChunkUpload } from '@/hooks/useChunkUpload';
import { UploadStatus, UploadTask, formatFileSize, formatUploadSpeed, formatRemainingTime, calculateUploadSpeed, estimateRemainingTime } from '@/utils/chunkUpload';

const { Text } = Typography;

interface FileUploaderProps {
  collectDate: string;
  linbanId: string;
  linbanName: string;
  collector: string;
  remark?: string;
  maxFileSize?: number; // 最大文件大小（字节）
  allowedTypes?: string[]; // 允许的文件类型
  enableChunkUpload?: boolean; // 是否启用分片上传
  chunkUploadThreshold?: number; // 分片上传阈值（字节）
  onUploadProgress?: (fileId: string, progress: number) => void;
  onUploadComplete?: (fileId: string, filePath: string) => void;
  onUploadError?: (fileId: string, error: string) => void;
}

const FileUploader: React.FC<FileUploaderProps> = ({
  collectDate,
  linbanId,
  linbanName,
  collector,
  remark,
  maxFileSize = 100 * 1024 * 1024, // 默认100MB
  allowedTypes,
  enableChunkUpload = true,
  chunkUploadThreshold = 10 * 1024 * 1024, // 默认10MB启用分片上传
  onUploadProgress,
  onUploadComplete,
  onUploadError,
}) => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploadTasks, setUploadTasks] = useState<Map<string, UploadTask>>(new Map());

  // 初始化分片上传Hook
  const chunkUpload = useChunkUpload({
    maxConcurrency: 3,
    enableRetry: true,
    onProgress: (task) => {
      setUploadTasks(prev => new Map(prev.set(task.id, task)));
      onUploadProgress?.(task.id, task.progress);
    },
    onSuccess: (task, fileUrl) => {
      setUploadTasks(prev => new Map(prev.set(task.id, task)));
      onUploadComplete?.(task.id, fileUrl);
      message.success(`文件 ${task.file.name} 上传成功！`);
    },
    onError: (task, error) => {
      setUploadTasks(prev => new Map(prev.set(task.id, task)));
      onUploadError?.(task.id, error);
      message.error(`文件 ${task.file.name} 上传失败: ${error}`);
    },
    onStatusChange: (task) => {
      setUploadTasks(prev => new Map(prev.set(task.id, task)));
    }
  });

  // 文件验证
  const validateFile = useCallback((file: File): string | null => {
    // 检查文件大小
    if (file.size > maxFileSize) {
      return `文件大小不能超过 ${formatFileSize(maxFileSize)}`;
    }

    // 检查文件类型
    if (allowedTypes && allowedTypes.length > 0) {
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      const isValidType = allowedTypes.some(type => {
        if (type.includes('/')) {
          // MIME类型检查
          if (type.endsWith('/*')) {
            return file.type.startsWith(type.replace('/*', '/'));
          }
          return file.type === type;
        } else {
          // 扩展名检查
          return fileExtension === type.toLowerCase();
        }
      });

      if (!isValidType) {
        return `不支持的文件类型，仅支持: ${allowedTypes.join(', ')}`;
      }
    }

    return null;
  }, [maxFileSize, allowedTypes]);

  // 处理文件上传
  const handleUpload = async (file: File) => {
    // 文件验证
    const validationError = validateFile(file);
    if (validationError) {
      message.error(validationError);
      return false;
    }

    try {
      // 判断是否使用分片上传
      const shouldUseChunkUpload = enableChunkUpload && file.size > chunkUploadThreshold;

      if (shouldUseChunkUpload) {
        // 使用分片上传
        await chunkUpload.uploadFile(file, {
          path: `${collectDate}/${linbanName}/`
        });
      } else {
        // 使用普通上传
        const params: FileUploadParams = {
          file,
          linbanId: parseInt(linbanId),
          linbanName,
          collector,
          collectDate,
          description: remark,
        };

        const response = await apiUploadFile(params);
        onUploadComplete?.(file.name, `文件上传成功，文件ID: ${response.data}`);
        message.success('文件上传成功！');
      }
    } catch (error: any) {
      const errorMsg = error.response?.data?.msg || error.message || '上传失败';
      onUploadError?.(file.name, errorMsg);
      message.error(`文件上传失败: ${errorMsg}`);
    }

    return false; // 阻止默认上传行为
  };

  // 移除文件
  const handleRemove = (file: UploadFile) => {
    // 如果是上传任务，取消上传
    const task = uploadTasks.get(file.uid);
    if (task && (task.status === UploadStatus.UPLOADING || task.status === UploadStatus.PENDING)) {
      chunkUpload.cancelUpload(task.id);
    }

    setUploadTasks(prev => {
      const newMap = new Map(prev);
      newMap.delete(file.uid);
      return newMap;
    });

    setFileList(prev => prev.filter(item => item.uid !== file.uid));
  };

  // 暂停上传
  const handlePause = (taskId: string) => {
    chunkUpload.pauseUpload(taskId);
  };

  // 恢复上传
  const handleResume = (taskId: string) => {
    chunkUpload.resumeUpload(taskId);
  };

  // 取消上传
  const handleCancel = (taskId: string) => {
    chunkUpload.cancelUpload(taskId);
  };

  // 获取状态标签颜色
  const getStatusColor = (status: UploadStatus) => {
    switch (status) {
      case UploadStatus.PENDING:
        return 'default';
      case UploadStatus.UPLOADING:
        return 'processing';
      case UploadStatus.PAUSED:
        return 'warning';
      case UploadStatus.COMPLETED:
        return 'success';
      case UploadStatus.FAILED:
        return 'error';
      case UploadStatus.CANCELLED:
        return 'default';
      default:
        return 'default';
    }
  };

  // 获取状态文本
  const getStatusText = (status: UploadStatus) => {
    switch (status) {
      case UploadStatus.PENDING:
        return '等待中';
      case UploadStatus.UPLOADING:
        return '上传中';
      case UploadStatus.PAUSED:
        return '已暂停';
      case UploadStatus.COMPLETED:
        return '已完成';
      case UploadStatus.FAILED:
        return '上传失败';
      case UploadStatus.CANCELLED:
        return '已取消';
      default:
        return '未知';
    }
  };

  // 渲染上传任务卡片
  const renderTaskCard = (task: UploadTask) => {
    const speed = task.startTime ? calculateUploadSpeed(
      task.uploadedChunks * task.chunkSize,
      task.startTime
    ) : 0;

    const remainingTime = estimateRemainingTime(
      task.file.size,
      task.uploadedChunks * task.chunkSize,
      speed
    );

    return (
      <Card
        key={task.id}
        size="small"
        style={{ marginBottom: 8 }}
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Text ellipsis style={{ maxWidth: '60%' }} title={task.file.name}>
              {task.file.name}
            </Text>
            <Tag color={getStatusColor(task.status)}>
              {getStatusText(task.status)}
            </Tag>
          </div>
        }
        extra={
          <Space>
            {task.status === UploadStatus.UPLOADING && (
              <Button
                size="small"
                icon={<PauseOutlined />}
                onClick={() => handlePause(task.id)}
              />
            )}
            {task.status === UploadStatus.PAUSED && (
              <Button
                size="small"
                icon={<PlayCircleOutlined />}
                onClick={() => handleResume(task.id)}
              />
            )}
            {(task.status === UploadStatus.UPLOADING ||
              task.status === UploadStatus.PAUSED ||
              task.status === UploadStatus.PENDING) && (
              <Button
                size="small"
                danger
                icon={<CloseOutlined />}
                onClick={() => handleCancel(task.id)}
              />
            )}
            {(task.status === UploadStatus.COMPLETED ||
              task.status === UploadStatus.FAILED ||
              task.status === UploadStatus.CANCELLED) && (
              <Button
                size="small"
                danger
                icon={<CloseOutlined />}
                onClick={() => handleRemove({ uid: task.id } as UploadFile)}
              />
            )}
          </Space>
        }
      >
        <div>
          <div style={{ marginBottom: 8 }}>
            <Progress
              percent={Math.round(task.progress)}
              status={
                task.status === UploadStatus.FAILED ? 'exception' :
                task.status === UploadStatus.COMPLETED ? 'success' : 'active'
              }
              size="small"
            />
          </div>

          <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px', color: '#666' }}>
            <span>
              {formatFileSize(task.uploadedChunks * task.chunkSize)} / {formatFileSize(task.file.size)}
            </span>
            {task.status === UploadStatus.UPLOADING && speed > 0 && (
              <span>
                {formatUploadSpeed(speed)} • {formatRemainingTime(remainingTime)}
              </span>
            )}
          </div>

          {task.status === UploadStatus.UPLOADING && (
            <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
              分片进度: {task.uploadedChunks} / {task.totalChunks}
            </div>
          )}

          {task.error && (
            <div style={{ fontSize: '12px', color: '#ff4d4f', marginTop: 4 }}>
              错误: {task.error}
            </div>
          )}
        </div>
      </Card>
    );
  };

  return (
    <div>
      <Upload
        multiple
        beforeUpload={handleUpload}
        onRemove={handleRemove}
        fileList={fileList}
        showUploadList={false}
      >
        <Button icon={<UploadOutlined />} block>
          选择文件上传
        </Button>
      </Upload>

      {uploadTasks.size > 0 && (
        <div style={{ marginTop: 16 }}>
          <div style={{ marginBottom: 8, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Text strong>上传任务</Text>
            <Button
              size="small"
              onClick={() => {
                chunkUpload.clearCompletedTasks();
                setUploadTasks(new Map());
              }}
            >
              清理已完成
            </Button>
          </div>
          {Array.from(uploadTasks.values()).map(renderTaskCard)}
        </div>
      )}

      <div style={{ marginTop: '12px', fontSize: '12px', color: '#666' }}>
        <div>• 支持批量上传多个文件</div>
        <div>• 推荐文件格式：GeoJSON、TIF、SHP、PDF、图片等</div>
        <div>• 大文件自动启用分片上传和断点续传</div>
        <div>• 单个文件建议不超过 {formatFileSize(maxFileSize)}</div>
      </div>
    </div>
  );
};

export default FileUploader; 
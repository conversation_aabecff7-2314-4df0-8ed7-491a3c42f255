.custom-table {
  :global(.ant-table-thead > tr > th) {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 2px solid #e2e8f0;
    font-weight: 600;
    color: #334155;
    padding: 8px 6px;
    
    &:first-child {
      border-top-left-radius: 8px;
    }
    
    &:last-child {
      border-top-right-radius: 8px;
    }
  }
  
  :global(.ant-table-tbody > tr) {
    &:hover > td {
      background-color: #f8fafc !important;
    }
    
    > td {
      padding: 8px 6px;
      border-bottom: 1px solid #f1f5f9;
      vertical-align: middle;
      
      &:first-child {
        padding-left: 8px;
      }
      
      &:last-child {
        padding-right: 8px;
      }
    }
    
    &:last-child > td {
      border-bottom: none;
    }
  }
  
  :global(.ant-table-placeholder) {
    :global(.ant-table-tbody > tr.ant-table-placeholder > td) {
      border-bottom: none;
    }
  }
}

.file-info-card {
  .file-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    
    .anticon {
      font-size: 18px;
    }
  }
}

.data-management-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 20px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 12px rgba(102, 126, 234, 0.15);
  
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .title-section {
      h2 {
        color: white;
        margin: 0 0 4px 0;
        font-size: 22px;
        font-weight: 700;
      }
      
      .subtitle {
        color: rgba(255, 255, 255, 0.8);
        font-size: 13px;
      }
    }
    
    .action-section {
      .ant-btn-primary {
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        font-weight: 600;
        height: 40px;
        padding: 0 24px;
        border-radius: 8px;
        backdrop-filter: blur(10px);
        
        &:hover {
          background: rgba(255, 255, 255, 0.3);
          border-color: rgba(255, 255, 255, 0.5);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }
}

.status-indicator {
  &.uploading {
    .ant-progress-line {
      .ant-progress-bg {
        background: linear-gradient(90deg, #1890ff, #52c41a);
      }
    }
  }
}

.action-dropdown {
  .ant-btn {
    border-radius: 6px;
    transition: all 0.2s ease;
    
    &:hover {
      transform: scale(1.05);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
  
  :global(.ant-table-wrapper) {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    .ant-table {
      border-radius: 0;
      flex: 1;
    }
    
    .ant-table-container {
      flex: 1;
      overflow: auto;
    }
    
    .ant-pagination {
      margin: 8px 20px 8px 0;
      flex-shrink: 0;
      border-top: 1px solid #f0f0f0;
      padding: 8px 0;
      text-align: center;
      white-space: nowrap;
      display: flex;
      justify-content: center;
      align-items: center;
      
      .ant-pagination-item {
        border-radius: 4px;
        
        &.ant-pagination-item-active {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-color: #667eea;
        }
      }
      
      .ant-pagination-options {
        white-space: nowrap;
      }
      
      .ant-pagination-total-text {
        white-space: nowrap;
      }
    }
  }
}

// 确保整体布局正确
:global(.ant-layout) {
  height: 100vh;
}

:global(.ant-layout-content) {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
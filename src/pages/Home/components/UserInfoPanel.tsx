import React, { FC, useState, useEffect } from 'react';
import { Card, Button, Slider, Select, DatePicker, Space, Spin } from 'antd';
import { CloseOutlined, PlayCircleOutlined, PauseCircleOutlined, SearchOutlined } from '@ant-design/icons';
import type { Dayjs } from 'dayjs';

const { RangePicker } = DatePicker;

interface UserInfoPanelProps {
  user: { id: number; name: string; grade: string; phone: string; job_id?: string };
  coordinate: { lon: number; lat: number };
  onClose: () => void;
  progress: number;
  isPlaying: boolean;
  speed: number;
  hasTrajectoryData: boolean;
  isLoadingTrajectory: boolean;
  currentTime: string;
  playbackTime: string;
  onPlay: () => void;
  onPause: () => void;
  onSeek: (fraction: number) => void;
  onSpeedChange: (speed: number) => void;
  onQueryTrajectory: (userId: number, startTime?: string, endTime?: string) => void;
}

const UserInfoPanel: FC<UserInfoPanelProps> = ({ 
  user, 
  coordinate, 
  onClose, 
  progress, 
  isPlaying, 
  speed, 
  hasTrajectoryData,
  isLoadingTrajectory,
  currentTime,
  playbackTime,
  onPlay, 
  onPause, 
  onSeek, 
  onSpeedChange,
  onQueryTrajectory 
}) => {
  const [showControls, setShowControls] = useState(false);
  const [timeRange, setTimeRange] = useState<[Dayjs | null, Dayjs | null] | null>(null);
  const [jobName, setJobName] = useState<string>('未知工种');

  // 获取工种名称
  useEffect(() => {
    if (user.job_id) {
      fetch('https://www.senfalinye.com/pounds/Gis.Index/getJobList', { method: 'POST' })
        .then(res => res.json())
        .then(data => {
          if (data.code === 1 && Array.isArray(data.data)) {
            const job = data.data.find((j: any) => j.id.toString() === user.job_id);
            if (job) {
              setJobName(job.job_name);
            }
          }
        })
        .catch(err => {
          console.error('获取工种信息失败', err);
        });
    }
  }, [user.job_id]);

  const handleQuery24Hours = () => {
    onQueryTrajectory(user.id);
    setShowControls(true);
  };

  const handleQueryCustomRange = () => {
    if (!timeRange || !timeRange[0] || !timeRange[1]) {
      return;
    }
    
    const startTime = timeRange[0].format('YYYY-MM-DD HH:mm:ss');
    const endTime = timeRange[1].format('YYYY-MM-DD HH:mm:ss');
    onQueryTrajectory(user.id, startTime, endTime);
    setShowControls(true);
  };

  return (
    <Card
      title={`用户信息 - ${user.name}`}
      size="small"
      extra={<Button type="text" icon={<CloseOutlined />} onClick={onClose} />}
      style={{
        position: 'absolute',
        right: 20,
        top: 90,
        width: 350,
        zIndex: 500,
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      }}
    >
      <div style={{ marginBottom: '16px' }}>
        <div style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#333' }}>
          姓名: {user.name}
        </div>
        
        <div style={{ fontSize: '14px', marginBottom: '8px', color: '#333' }}>
          电话: {user.phone}
        </div>
        
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginBottom: '12px' }}>
          <span style={{
            background: '#e3f2fd',
            color: '#1976d2',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '12px',
            border: '1px solid #bbdefb'
          }}>
            工种: {jobName}
          </span>
          
          <span style={{
            background: '#e8f5e8',
            color: '#2e7d32',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '12px',
            border: '1px solid #c8e6c9'
          }}>
            组长: 暂无
          </span>
          
          <span style={{
            background: '#fff3e0',
            color: '#f57c00',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '12px',
            border: '1px solid #ffcc02'
          }}>
            等级: {user.grade}
          </span>
        </div>
        
        <div style={{ fontSize: '14px', color: '#666', lineHeight: '1.5' }}>
          <div><strong>坐标:</strong></div>
          <div>经度: {coordinate.lon.toFixed(6)}</div>
          <div>纬度: {coordinate.lat.toFixed(6)}</div>
        </div>
      </div>
      
      {!showControls ? (
        <div style={{ marginTop: 12 }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Button 
              type="primary" 
              onClick={handleQuery24Hours}
              loading={isLoadingTrajectory}
              block
            >
              查看24小时轨迹
            </Button>
            
            <div>
              <p style={{ margin: '8px 0 4px 0', fontSize: '12px', color: '#666' }}>
                自定义时间范围:
              </p>
              <RangePicker
                showTime
                format="YYYY-MM-DD HH:mm:ss"
                style={{ width: '100%', marginBottom: 8 }}
                onChange={(dates) => setTimeRange(dates)}
                placeholder={['开始时间', '结束时间']}
              />
              <Button
                type="default"
                icon={<SearchOutlined />}
                onClick={handleQueryCustomRange}
                loading={isLoadingTrajectory}
                disabled={!timeRange || !timeRange[0] || !timeRange[1]}
                block
              >
                查询轨迹
              </Button>
            </div>
          </Space>
        </div>
      ) : (
        <div style={{ marginTop: 12 }}>
          <Spin spinning={isLoadingTrajectory}>
            <Space direction="vertical" style={{ width: '100%' }}>
              {hasTrajectoryData && (
                <>
                  <div>
                    <Slider
                      min={0}
                      max={100}
                      value={Math.round(progress * 100)}
                      onChange={(val) => onSeek(val / 100)}
                      disabled={isLoadingTrajectory}
                    />
                                         <div style={{ 
                       display: 'flex', 
                       justifyContent: 'space-between', 
                       fontSize: '11px', 
                       color: '#999',
                       marginTop: '-8px',
                       marginBottom: '8px'
                     }}>
                       <span>进度: {Math.round(progress * 100)}%</span>
                       <span>{isPlaying ? '播放中' : '已暂停'} ({speed}x)</span>
                     </div>
                     
                     {/* 时间信息显示 */}
                     <div style={{ 
                       backgroundColor: '#f5f5f5', 
                       padding: '8px', 
                       borderRadius: '4px',
                       marginBottom: '8px'
                     }}>
                       <div style={{ fontSize: '12px', marginBottom: '4px' }}>
                         <span style={{ color: '#666' }}>当前时间: </span>
                         <span style={{ color: '#1890ff', fontWeight: 'bold' }}>
                           {currentTime || '未开始'}
                         </span>
                       </div>
                       <div style={{ fontSize: '11px', color: '#999' }}>
                         <span>播放时长: {playbackTime || '0秒'}</span>
                       </div>
                     </div>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Button
                      type="primary"
                      shape="circle"
                      icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                      onClick={() => { if (isPlaying) onPause(); else onPlay(); }}
                      disabled={isLoadingTrajectory || !hasTrajectoryData}
                    />
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                      <span style={{ fontSize: '12px', color: '#666' }}>倍速:</span>
                                              <Select
                          value={speed}
                          onChange={(val) => onSpeedChange(val)}
                          style={{ width: 80 }}
                          disabled={isLoadingTrajectory}
                          size="small"
                          options={[
                            { label: '1x', value: 1 },
                            { label: '5x', value: 5 },
                            { label: '10x', value: 10 },
                            { label: '20x', value: 20 },
                          ]}
                        />
                    </div>
                  </div>
                </>
              )}
              
              <div>
                <p style={{ margin: '8px 0 4px 0', fontSize: '12px', color: '#666' }}>
                  重新查询:
                </p>
                <RangePicker
                  showTime
                  format="YYYY-MM-DD HH:mm:ss"
                  style={{ width: '100%', marginBottom: 8 }}
                  onChange={(dates) => setTimeRange(dates)}
                  placeholder={['开始时间', '结束时间']}
                />
                <Button
                  type="default"
                  icon={<SearchOutlined />}
                  onClick={handleQueryCustomRange}
                  loading={isLoadingTrajectory}
                  disabled={!timeRange || !timeRange[0] || !timeRange[1]}
                  block
                  size="small"
                >
                  查询轨迹
                </Button>
              </div>
            </Space>
          </Spin>
        </div>
      )}
    </Card>
  );
};

export default UserInfoPanel; 
import React, { FC, useEffect } from 'react';
import { Card, Form, Input, Select, Button, Spin } from 'antd';
import { useGetAllLinban } from '@/api/linban';
import { useUserStore } from '@/store/user';

const { Option } = Select;

interface DrawingInfoPanelProps {
  onSave: (values: any) => void;
  onCancel: () => void;
  loading: boolean;
}

const DrawingInfoPanel: FC<DrawingInfoPanelProps> = ({ onSave, onCancel, loading }) => {
  const [form] = Form.useForm();
  const userId = useUserStore((state) => state.linBanUser.id);
  const { data: linbanData, isLoading: linbanLoading } = useGetAllLinban(userId ? String(userId) : "");

  return (
    <Card
      title="保存绘制"
      size="small"
      style={{
        position: 'absolute',
        right: '20px',
        top: '90px',
        width: 300,
        zIndex: 500, // Ensure it's above other panels
        backgroundColor: 'rgba(255, 255, 255, 0.98)',
        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      }}
    >
      <Spin spinning={linbanLoading || loading} tip="正在保存...">
        <Form form={form} layout="vertical" onFinish={onSave}>
          <Form.Item
            label="名称"
            name="name"
            rules={[{ required: true, message: '请输入名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item label="描述" name="description">
            <Input.TextArea rows={2} />
          </Form.Item>
          <Form.Item label="所属林班" name="quartelId">
            <Select placeholder="选择一个林班 (可选)">
              {linbanData?.data?.map((linban: any) => (
                <Option key={linban.id} value={linban.id}>
                  {linban.quatelName}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
            <Button onClick={onCancel} style={{ marginRight: 8 }}>
              取消
            </Button>
            <Button type="primary" htmlType="submit">
              保存
            </Button>
          </Form.Item>
        </Form>
      </Spin>
    </Card>
  );
};

export default DrawingInfoPanel; 
import React, { FC, useState } from 'react';
import { Segmented } from 'antd';
import { LeftOutlined, RightOutlined, UserOutlined, TeamOutlined, HeartOutlined, ClusterOutlined } from '@ant-design/icons';
import UserListPanel from './UserListPanel';
import ForestPlotPanel from './ForestPlotPanel';
import FavoritesPanel from './FavoritesPanel';
import { Map as OlMap } from 'ol';

interface SidePanelProps {
  mapInstance: OlMap | null;
  onUserSelect: (user: { id: number; name: string; grade: string; phone: string; job_id: string }, coordinate: { lon: number; lat: number }) => void;
  onPlotSelect?: (plot: any) => void;
  onFavoriteSelect?: (feature: any) => void;
  onDrawSelect?: (type: string) => void;
}

const SidePanel: FC<SidePanelProps> = ({ mapInstance, onUserSelect, onPlotSelect, onFavoriteSelect }) => {
  const [sidebarExpanded, setSidebarExpanded] = useState(true);
  const [activeTab, setActiveTab] = useState('jobs');

  const toggleSidebar = () => {
    setSidebarExpanded(!sidebarExpanded);
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'jobs':
        return <UserListPanel onUserSelect={onUserSelect} />;
      case 'plots':
        return <ForestPlotPanel map={mapInstance} onPlotSelect={onPlotSelect} />;
      case 'favorites':
        return <FavoritesPanel onFavoriteSelect={onFavoriteSelect} />;
      default:
        return null;
    }
  };

  // 添加样式
  React.useEffect(() => {
    const style = document.createElement('style');
    style.innerHTML = `
      .sidebar-container {
        position: relative;
        height: 100%;
      }
      
      .sidebar {
        background: white;
        height: 100%;
        transition: all 0.3s ease;
        border-right: 1px solid #f0f0f0;
        overflow: hidden;
      }
      
      .sidebar-collapsed {
        width: 0px;
        padding: 0;
        border-right: none;
      }
      
      .sidebar-expanded {
        width: 300px;
      }
      
      .toggle-button {
        position: absolute;
        left: ${sidebarExpanded ? '300px' : '0px'};
        top: 50%;
        transform: translateY(-50%);
        width: 24px;
        height: 48px;
        background: white;
        border: 1px solid #f0f0f0;
        border-radius: 0 12px 12px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 100;
        box-shadow: 2px 0 8px rgba(0,0,0,0.1);
        cursor: pointer;
        outline: none;
        transition: all 0.3s ease;
        opacity: 0.8;
        font-size: 12px;
      }
      
      .toggle-button:hover {
        background: #f5f5f5;
        opacity: 1;
      }
      
      .toggle-button .anticon {
        transition: transform 0.3s ease;
      }
      
      .sidebar-content {
        width: 300px;
        height: 100%;
        display: flex;
        flex-direction: column;
        padding: 20px;
        opacity: 1;
        transition: opacity 0.3s ease;
      }
      
      .sidebar-content-collapsed {
        opacity: 0;
        pointer-events: none;
      }
      
      /* 移除自定义标签页样式 */
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, [sidebarExpanded]);

  return (
    <div className="sidebar-container">
      {/* 左侧侧边栏 */}
      <div className={`sidebar ${sidebarExpanded ? 'sidebar-expanded' : 'sidebar-collapsed'}`}>
        <div className={`sidebar-content ${!sidebarExpanded ? 'sidebar-content-collapsed' : ''}`}>
          {/* <h1 style={{ marginBottom: '16px', fontSize: '18px', flexShrink: 0 }}>欢迎使用地图</h1> */}
          
          <div style={{ flexShrink: 0 }}>
            <Segmented
              options={[
                { label: '工种分组', value: 'jobs'},
                { label: '林班分组', value: 'plots'},
                { label: '收藏夹', value: 'favorites'},
              ]}
              value={activeTab}
              onChange={(value) => setActiveTab(value as string)}
              block
            />
          </div>
          
          <div style={{ flex: 1, overflow: 'auto' }}>
            {renderContent()}
          </div>
        </div>
      </div>
      
      {/* 切换按钮 */}
      <button 
        className="toggle-button" 
        onClick={toggleSidebar} 
        style={{ 
          left: sidebarExpanded ? '300px' : '0px',
          transition: 'left 0.3s ease'
        }}
        title={sidebarExpanded ? "收起面板" : "展开面板"}
      >
        {sidebarExpanded ? <LeftOutlined /> : <RightOutlined />}
      </button>
    </div>
  );
};

export default SidePanel; 
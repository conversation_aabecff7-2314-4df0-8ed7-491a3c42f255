import React, { useMemo, useState, useEffect } from 'react';
import { List, Avatar, Spin, Empty, Collapse, Input, Tabs, Tree, Checkbox, Button, message } from 'antd';
import { 
  HeartOutlined, 
  UserOutlined, 
  TeamOutlined, 
  FolderOutlined,
  FileOutlined,
  BorderOuterOutlined,
  EditOutlined,
  CloudOutlined,
  ProfileOutlined,
  CameraOutlined,
} from '@ant-design/icons';
import { useGetFavorites, useGetAllLinban } from '@/api/linban';
import { useUserStore } from '@/store/user';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';

const { Panel } = Collapse;
const { Search } = Input;
const { TabPane } = Tabs;
const { DirectoryTree } = Tree;

interface FavoritesPanelProps {
  onFavoriteSelect?: (feature: any) => void;
}

// 个人收藏组件
const PersonalFavorites: React.FC<FavoritesPanelProps> = ({ onFavoriteSelect }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const userIdNum = useUserStore((state) => state.linBanUser.id);
  const userId = userIdNum ? String(userIdNum) : '';

  const { data: favoritesData, isLoading: favoritesLoading } = useGetFavorites(userId);
  const { data: linbanData, isLoading: linbanLoading } = useGetAllLinban(userId);

  const groupedFavorites = useMemo(() => {
    if (!favoritesData?.data?.all || !linbanData?.data) {
      return [];
    }

    const linbanMap = new Map(linbanData.data.map((l: any) => [l.id, l.quatelName]));
    const groups: { [key: string]: { name: string; id: any; favorites: any[] } } = {
      'un-grouped': { name: '未分组', id: 'un-grouped', favorites: [] },
    };

    favoritesData.data.all.forEach((fav: any) => {
      const groupId = fav.quartelId || 'un-grouped';
      if (!groups[groupId]) {
        groups[groupId] = {
          id: groupId,
          name: (linbanMap.get(groupId) as string) || `林班 ${groupId}`,
          favorites: [],
        };
      }
      groups[groupId].favorites.push({
        id: fav.id,
        name: fav.labelName,
        type: fav.type || '未知类型',
        dataJson: fav.dataJson,
      });
    });
    
    return Object.values(groups).filter(g => g.favorites.length > 0);
  }, [favoritesData, linbanData]);

  const filteredGroups = useMemo(() => {
    if (!searchTerm) {
      return groupedFavorites;
    }
    return groupedFavorites.map(group => {
      const filteredFavs = group.favorites.filter(fav =>
        fav.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
      return { ...group, favorites: filteredFavs };
    }).filter(group => group.favorites.length > 0);
  }, [groupedFavorites, searchTerm]);

  const isLoading = favoritesLoading || linbanLoading;

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <div style={{ padding: '8px 0', borderBottom: '1px solid #f0f0f0', flexShrink: 0 }}>
        <Search
          placeholder="搜索个人收藏"
          onChange={e => setSearchTerm(e.target.value)}
          style={{ width: '100%' }}
        />
      </div>
      <div className="tab-body" style={{ flex: 1, overflowY: 'auto' }}>
        {isLoading ? (
          <div style={{ textAlign: 'center', paddingTop: 50 }}>
            <Spin />
          </div>
        ) : filteredGroups.length > 0 ? (
          <div className="favorites-list-container" style={{ maxHeight: 'calc(100vh - 250px)', overflow: 'auto', padding: '0 5px' }}>
          <Collapse defaultActiveKey={filteredGroups.map(g => String(g.id))} ghost>
            {filteredGroups.map((group) => (
              <Panel header={group.name} key={String(group.id)}>
                <List
                  itemLayout="horizontal"
                  dataSource={group.favorites}
                  renderItem={(fav: any) => (
                    <List.Item
                      onClick={() => fav.dataJson && onFavoriteSelect && onFavoriteSelect(fav.dataJson)}
                      style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
                    >
                      <List.Item.Meta
                        avatar={<Avatar style={{ backgroundColor: '#ff4d4f' }} icon={<HeartOutlined />} />}
                        title={<span>{fav.name}</span>}
                      />
                    </List.Item>
                  )}
                />
              </Panel>
            ))}
          </Collapse>
          </div>
        ) : (
          <Empty description="暂无收藏" style={{ paddingTop: 50 }} />
        )}
      </div>
    </div>
  );
};

// 企业收藏组件 - 使用林班目录结构
const EnterpriseFavorites: React.FC<FavoritesPanelProps> = ({ onFavoriteSelect }) => {
  const userIdNum = useUserStore((state) => state.linBanUser.id);
  const userId = userIdNum ? String(userIdNum) : '';
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  
  // 获取林班列表
  const { data: linbanData, isLoading: linbanLoading } = useGetAllLinban(userId);
  
  // 获取收藏列表 (使用与个人收藏相同的API)
  const { data: favoritesData, isLoading: favoritesLoading } = useGetFavorites(userId);
  
  // 固定的五个子目录类型及其图标
  const subDirectoryTypes = [
    { key: 'boundary', title: '边界图', icon: <BorderOuterOutlined /> },
    { key: 'drawing', title: '勾绘图', icon: <EditOutlined /> },
    { key: 'temporary', title: '临时图', icon: <CloudOutlined /> },
    { key: 'task', title: '任务图', icon: <ProfileOutlined /> },
    { key: 'aerial', title: '航拍图', icon: <CameraOutlined /> }
  ];
  
  // 构建目录树数据
  const treeData = useMemo(() => {
    if (!linbanData?.data) return [];
    
    // 获取所有收藏项并按林班和类型分组
    const favoritesMap: Record<string, Record<string, any[]>> = {};
    
    // 预处理数据
    if (favoritesData?.data?.all) {
      favoritesData.data.all.forEach((fav: any) => {
        const linbanId = String(fav.quartelId || 'un-grouped');
        if (!favoritesMap[linbanId]) {
          favoritesMap[linbanId] = {};
        }
        
        // 根据标注类型确定子目录
        let subType = 'temporary'; // 默认放在临时图
        if (fav.type === 'Polygon' && fav.labelName.includes('边界')) {
          subType = 'boundary';
        } else if (fav.type === 'LineString' || fav.type === 'Polygon') {
          subType = 'drawing';
        } else if (fav.labelName.includes('任务')) {
          subType = 'task';
        } else if (fav.labelName.includes('航拍')) {
          subType = 'aerial';
        }
        
        if (!favoritesMap[linbanId][subType]) {
          favoritesMap[linbanId][subType] = [];
        }
        
        favoritesMap[linbanId][subType].push({
          id: fav.id,
          name: fav.labelName,
          type: fav.type || '未知类型',
          dataJson: fav.dataJson,
        });
      });
    }
    
    // 构建树结构
    return linbanData.data.map((linban: any) => {
      const linbanId = String(linban.id);
      return {
        title: linban.quartelName || `林班 ${linbanId}`,
        key: `linban-${linbanId}`,
        icon: <FolderOutlined style={{ color: '#1890ff' }} />,
        children: subDirectoryTypes.map(subDir => {
          const items = favoritesMap[linbanId]?.[subDir.key] || [];
          return {
            title: subDir.title,
            key: `${linbanId}-${subDir.key}`,
            icon: subDir.icon,
            // 确保即使没有数据也显示空目录
            children: items.length > 0 
              ? items.map((item, index) => ({
                  title: item.name,
                  key: `${linbanId}-${subDir.key}-${item.id}`,
                  isLeaf: true,
                  icon: <FileOutlined style={{ color: '#52c41a' }} />,
                  dataJson: item.dataJson
                }))
              : [] // 空数组表示没有子项但目录仍然存在
          };
        })
      };
    });
  }, [linbanData, favoritesData]);
  
  // 过滤搜索结果
  const filteredTreeData = useMemo(() => {
    if (!searchTerm) return treeData;
    
    const filterTree = (nodes: any[]): any[] => {
      return nodes.map(node => {
        if (node.isLeaf && node.title.toLowerCase().includes(searchTerm.toLowerCase())) {
          return { ...node };
        }
        
        if (node.children) {
          const filteredChildren = filterTree(node.children);
          if (filteredChildren.length > 0) {
            return { ...node, children: filteredChildren };
          }
        }
        
        if (node.title.toLowerCase().includes(searchTerm.toLowerCase())) {
          return { ...node };
        }
        
        return null;
      }).filter(Boolean);
    };
    
    return filterTree(treeData);
  }, [treeData, searchTerm]);

  // 初始展开第一个林班
  useEffect(() => {
    if (treeData.length > 0) {
      setExpandedKeys([treeData[0].key]);
    }
  }, [treeData]);
  
  // 选择处理
  const onCheck = (checked: React.Key[] | { checked: React.Key[]; halfChecked: React.Key[] }) => {
    if (Array.isArray(checked)) {
      setCheckedKeys(checked);
    } else {
      setCheckedKeys(checked.checked);
    }
  };

  // 选择节点
  const onSelect = (selectedKeys: React.Key[], info: any) => {
    setSelectedKeys(selectedKeys);
    
    // 点击叶子节点时，显示相应的地图数据
    if (info.node.isLeaf && info.node.dataJson) {
      onFavoriteSelect && onFavoriteSelect(info.node.dataJson);
    }
  };
  
  // 查找树中所有被勾选的叶子节点及其dataJson
  const findCheckedLeafNodes = () => {
    const getDataFromNodes = (keys: React.Key[]) => {
      const result: any[] = [];
      
      const findDataForKey = (nodes: any[], key: React.Key): boolean => {
        for (const node of nodes) {
          if (node.key === key) {
            if (node.isLeaf && node.dataJson) {
              result.push({ key: node.key, dataJson: node.dataJson });
            }
            return true;
          }
          
          if (node.children && node.children.length > 0) {
            if (findDataForKey(node.children, key)) {
              return true;
            }
          }
        }
        
        return false;
      };
      
      for (const key of keys) {
        for (const topNode of treeData) {
          findDataForKey([topNode], key);
        }
      }
      
      return result;
    };
    
    return getDataFromNodes(checkedKeys);
  };
  
  // 批量操作
  const handleBatchOperation = (operation: string) => {
    if (checkedKeys.length === 0) {
      message.info('请先选择要操作的项');
      return;
    }
    
    // 获取所有选中的叶子节点数据
    const checkedItems = findCheckedLeafNodes();
    
    if (checkedItems.length === 0) {
      message.info('所选项中没有可显示的数据');
      return;
    }
    
    // 执行不同操作
    switch (operation) {
      case 'show':
        // 向地图组件发送事件，批量显示所有选中项
        if (onFavoriteSelect) {
          // 这里简化为只显示第一个项目，实际应该发送批量显示事件
          // 注意: 要实现真正的批量显示，需要在主页面添加批量显示的功能
          // 目前的onFavoriteSelect只能显示一个项目
          onFavoriteSelect(checkedItems[0].dataJson);
          message.success(`已显示 ${checkedItems.length} 个项目中的第一个（当前仅支持单个显示）`);
        }
        break;
      case 'hide':
        // 向地图组件发送事件，批量隐藏所有选中项
        // 需要在Home组件中添加相应的隐藏事件处理
        message.info(`隐藏功能需要在主页面添加支持，当前尚未实现`);
        break;
      case 'delete':
        // 批量删除逻辑 - 需要添加删除API调用
        message.info(`删除功能需要添加API支持，当前尚未实现`);
        break;
      default:
        break;
    }
  };
  
  const isLoading = linbanLoading || favoritesLoading;

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <div style={{ padding: '8px 0', borderBottom: '1px solid #f0f0f0', flexShrink: 0 }}>
        <Search
          placeholder="搜索企业收藏"
          onChange={e => setSearchTerm(e.target.value)}
          style={{ width: '100%' }}
        />
      </div>
      
      <div style={{ marginTop: '8px', padding: '0 8px', display: 'flex', gap: '8px', flexShrink: 0 }}>
        <Button type="primary" size="small" onClick={() => handleBatchOperation('show')}>批量显示</Button>
        <Button size="small" onClick={() => handleBatchOperation('hide')}>批量隐藏</Button>
        <Button size="small" danger onClick={() => handleBatchOperation('delete')}>批量删除</Button>
      </div>
      
      <div className="tab-body" style={{ flex: 1, overflowY: 'auto', marginTop: '8px' }}>
        {isLoading ? (
          <div style={{ textAlign: 'center', paddingTop: 50 }}>
            <Spin />
          </div>
        ) : filteredTreeData.length > 0 ? (
          <div className="directory-tree-container" style={{ maxHeight: 'calc(100vh - 250px)', overflow: 'auto' }}>
          <DirectoryTree
            checkable
            showIcon
            defaultExpandAll
            expandedKeys={expandedKeys}
            checkedKeys={checkedKeys}
            selectedKeys={selectedKeys}
            onCheck={onCheck}
            onSelect={onSelect}
            onExpand={setExpandedKeys}
            treeData={filteredTreeData}
          />
          </div>
        ) : (
          <Empty description="暂无企业收藏" style={{ paddingTop: 50 }} />
        )}
      </div>
    </div>
  );
};

// 收藏夹主面板
const FavoritesPanel: React.FC<FavoritesPanelProps> = ({ onFavoriteSelect }) => {
  return (
    <div className="tab-content-panel" style={{ paddingBottom: "30px" }}>
      <style>
        {`
          .favorites-tabs .ant-tabs-content-holder {
            flex: 1;
            overflow: hidden;
          }
          .favorites-tabs .ant-tabs-nav {
            margin: 0;
          }
          .favorites-tabs .ant-tabs-tabpane {
            height: 100%;
          }

          /* 自定义滚动条样式 */
          .directory-tree-container,
          .favorites-list-container {
            padding: 0 5px;
          }
          .directory-tree-container::-webkit-scrollbar,
          .favorites-list-container::-webkit-scrollbar {
            width: 6px;
          }
          .directory-tree-container::-webkit-scrollbar-thumb,
          .favorites-list-container::-webkit-scrollbar-thumb {
            background-color: #d9d9d9;
            border-radius: 3px;
          }
          .directory-tree-container::-webkit-scrollbar-track,
          .favorites-list-container::-webkit-scrollbar-track {
            background-color: #f5f5f5;
          }
          .directory-tree-container:hover::-webkit-scrollbar-thumb,
          .favorites-list-container:hover::-webkit-scrollbar-thumb {
            background-color: #bfbfbf;
          }

          /* 列表样式优化 */
          .favorites-list-container .ant-collapse {
            background: transparent;
          }
          .favorites-list-container .ant-collapse-header {
            padding: 8px 0 !important;
            font-weight: 500;
          }

          /* 树形控件样式优化 */
          .directory-tree-container .ant-tree {
            padding-bottom: 10px;
          }
          .directory-tree-container .ant-tree-treenode {
            padding: 2px 0;
          }
          .directory-tree-container .ant-tree-node-content-wrapper {
            transition: all 0.2s;
          }
          .directory-tree-container .ant-tree-node-content-wrapper:hover {
            background-color: #e6f7ff;
          }
        `}
      </style>
      <Tabs
        className="favorites-tabs"
        defaultActiveKey="enterprise" // 默认选中企业收藏
        tabPosition="bottom"
        centered
        style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
      >
        <TabPane
          tab={
            <span>
              <UserOutlined />
              个人收藏
            </span>
          }
          key="personal"
        >
          <PersonalFavorites onFavoriteSelect={onFavoriteSelect} />
        </TabPane>
        <TabPane
          tab={
            <span>
              <TeamOutlined />
              企业收藏
            </span>
          }
          key="enterprise"
        >
          <EnterpriseFavorites onFavoriteSelect={onFavoriteSelect} />
        </TabPane>
      </Tabs>
    </div>
  );
};

export default FavoritesPanel; 
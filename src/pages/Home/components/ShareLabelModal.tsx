import React, { FC, useState, useEffect } from 'react';
import { Modal, Spin, List, Avatar, Checkbox, Button } from 'antd';
import { useUserStore } from '@/store/user';
import { IMSDK } from '@/layout/MainContentWrap';
import { MessageType, SessionType } from "@openim/wasm-client-sdk";

interface ShareLabelModalProps {
  visible: boolean;
  title?: string;
  onClose: () => void;
  onShare: (selectedUsers: any[]) => void;
  loading?: boolean;
}

const ShareLabelModal: FC<ShareLabelModalProps> = ({ 
  visible, 
  title = '分享标注',
  onClose, 
  onShare,
  loading = false 
}) => {
  const [friends, setFriends] = useState<any[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<any[]>([]);
  const [loadingFriends, setLoadingFriends] = useState(false);

  // 加载好友列表
  useEffect(() => {
    if (visible) {
      loadFriendList();
    } else {
      setSelectedUsers([]);
    }
  }, [visible]);

  const loadFriendList = async () => {
    try {
      setLoadingFriends(true);
      // 获取好友列表
      const { data } = await IMSDK.getFriendListPage({ offset: 0, count: 100 });
      setFriends(data || []);
    } catch (error) {
      console.error('获取好友列表失败:', error);
    } finally {
      setLoadingFriends(false);
    }
  };

  // 勾选/取消勾选用户
  const toggleSelectUser = (user: any) => {
    const isSelected = selectedUsers.some(item => item.userID === user.userID);
    if (isSelected) {
      setSelectedUsers(selectedUsers.filter(item => item.userID !== user.userID));
    } else {
      setSelectedUsers([...selectedUsers, user]);
    }
  };

  // 选择所有用户
  const selectAll = () => {
    if (selectedUsers.length === friends.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers([...friends]);
    }
  };

  // 处理分享操作
  const handleShare = () => {
    onShare(selectedUsers);
  };

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="cancel" onClick={onClose}>
          取消
        </Button>,
        <Button
          key="share"
          type="primary"
          loading={loading}
          disabled={selectedUsers.length === 0}
          onClick={handleShare}
        >
          分享给所选用户
        </Button>
      ]}
      maskClosable={false}
      width={400}
    >
      <Spin spinning={loadingFriends}>
        {friends.length > 0 ? (
          <>
            <div style={{ marginBottom: 10 }}>
              <Checkbox
                checked={selectedUsers.length === friends.length && friends.length > 0}
                indeterminate={selectedUsers.length > 0 && selectedUsers.length < friends.length}
                onChange={selectAll}
              >
                全选
              </Checkbox>
            </div>
            <List
              itemLayout="horizontal"
              dataSource={friends}
              style={{ maxHeight: '50vh', overflow: 'auto' }}
              renderItem={user => {
                const friendInfo = user.friendInfo || {};
                return (
                  <List.Item
                    onClick={() => toggleSelectUser(user)}
                    style={{ cursor: 'pointer' }}
                  >
                    <Checkbox
                      checked={selectedUsers.some(item => item.userID === user.userID)}
                    />
                    <List.Item.Meta
                      avatar={<Avatar src={user.faceURL} />}
                      title={user.remark || user.nickname || user.userID}
                      style={{ marginLeft: 8 }}
                    />
                  </List.Item>
                );
              }}
            />
          </>
        ) : (
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            {loadingFriends ? '加载中...' : '暂无好友'}
          </div>
        )}
      </Spin>
    </Modal>
  );
};

export default ShareLabelModal; 
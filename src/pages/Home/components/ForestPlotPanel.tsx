import React, { useEffect, useState, FC, useMemo } from 'react';
import { Button, Input, List, Tag, Spin, Collapse, Avatar } from 'antd';
import { PlusOutlined, DeleteOutlined, UserOutlined } from '@ant-design/icons';
import { Map as OlMap } from 'ol';
import VectorSource from 'ol/source/Vector';
import VectorLayer from 'ol/layer/Vector';
import Feature from 'ol/Feature';
import Polygon from 'ol/geom/Polygon';
import { Fill, Stroke, Style } from 'ol/style';
import { fromLonLat } from 'ol/proj';
import { useGetAllLinban, useGetAllInLinban } from '@/api/linban';
import { useUserStore } from '@/store/user';

const { Search } = Input;
const { Panel } = Collapse;

interface ForestPlotPanelProps {
  map: OlMap | null;
  onPlotSelect?: (plot: any) => void;
}

interface ForestPlot {
  id: number;
  name: string;
  area: string;
  type: string;
  coordinates: any[];
  personnel?: any[];
  personnelLoading?: boolean;
}

const ForestPlotPanel: FC<ForestPlotPanelProps> = ({ map, onPlotSelect }) => {
  // 从 Zustand 获取当前用户ID
  const userId = useUserStore((state) => state.linBanUser.id);
  // 调用接口获取林班列表
  const { data: linbanData, isLoading: linbanLoading } = useGetAllLinban(userId ? String(userId) : "");
  // 本地状态管理林班数据和选中项
  const [forestPlots, setForestPlots] = useState<ForestPlot[]>([]);
  const [selectedPlotId, setSelectedPlotId] = useState<number | null>(null);
  const [activeCollapseKeys, setActiveCollapseKeys] = useState<string | string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');

  // 获取林班下人员的 mutation
  const { mutate: fetchPersonnel } = useGetAllInLinban();

  // 将获取到的接口数据同步到本地状态
  useEffect(() => {
    if (linbanData?.data) {
      
      const formattedData: ForestPlot[] = linbanData.data.map((item: any) => ({
        id: item.id,
        name: item.quartelName || "未命名林班",
        area: item.linbanArea || "未知面积",
        type: item.dominantTreeSpecies || "未知类型",
        coordinates: item.coordinates || [],
      }));
      setForestPlots(formattedData);
      // 默认展开第一个林班
      if (formattedData.length > 0) {
        // setActiveCollapseKeys([String(formattedData[0].id)]);
        // handleCollapseChange([String(formattedData[0].id)]);
      }
    }
  }, [linbanData]);

  const filteredForestPlots = useMemo(() => {
    if (!searchTerm) {
      return forestPlots;
    }
    return forestPlots.filter(plot =>
      plot.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [forestPlots, searchTerm]);

  // 添加林班多边形到地图
  const addPlotsToMap = () => {
    if (!map) return;

    // 查找或创建林班图层
    let forestLayer = map.getLayers().getArray().find(layer => 
      layer.get('name') === '林班多边形'
    ) as VectorLayer<VectorSource> | undefined;

    if (!forestLayer) {
      const forestSource = new VectorSource();
      forestLayer = new VectorLayer({
        source: forestSource,
        style: function(feature) {
          return new Style({
            fill: new Fill({
              color: 'rgba(51, 153, 204, 0.2)'
            }),
            stroke: new Stroke({
              color: '#3399cc',
              width: 2
            })
          });
        },
        properties: {
          name: '林班多边形'
        }
      });
      map.addLayer(forestLayer);
    }

    // 确保forestLayer和source存在
    const source = forestLayer.getSource();
    if (!source) return;
    
    // 清除现有要素
    source.clear();

    // 只添加过滤后的多边形
    filteredForestPlots.forEach(plot => {
      // 转换坐标格式为OpenLayers所需的格式
      if (!plot.coordinates || plot.coordinates.length === 0) return;
      const coordinates = plot.coordinates.map((coord: any) => fromLonLat(coord));
      
      const polygon = new Polygon([coordinates]);
      const feature = new Feature({
        geometry: polygon,
        name: plot.name,
        area: plot.area,
        type: plot.type,
        id: plot.id
      });
      
      source.addFeature(feature);
    });
  };

  // 在地图加载后添加林班多边形
  useEffect(() => {
    if (map) {
      addPlotsToMap();
    }
  }, [map, filteredForestPlots]);

  // 高亮显示选中的林班
  const highlightPlotOnMap = (plotId: number | null) => {
    setSelectedPlotId(plotId);

    if (map) {
      const forestLayer = map.getLayers().getArray().find(layer => 
        layer.get('name') === '林班多边形'
      ) as VectorLayer<VectorSource> | undefined;
      
      if (forestLayer) {
        const source = forestLayer.getSource();
        if (source) {
          source.getFeatures().forEach(feature => {
            const featureId = feature.get('id');
            if (featureId === plotId) {
              feature.setStyle(
                new Style({
                  fill: new Fill({
                    color: "rgba(255, 204, 51, 0.4)",
                  }),
                  stroke: new Stroke({
                    color: "#ffcc33",
                    width: 3,
                  }),
                }),
              );

              // 将地图中心设置到林班
              const geometry = feature.getGeometry();
              if (geometry) {
                const extent = geometry.getExtent();
                // an empty extent is [Infinity, Infinity, -Infinity, -Infinity]
                if (extent[0] < Infinity) {
                  map.getView().fit(extent, {
                    padding: [100, 100, 100, 100],
                    maxZoom: 16,
                  });
                }
              }
            } else {
              feature.setStyle(undefined);
            }
          });
        }
      }
    }
  };
  
  // 折叠面板变化时触发
  const handleCollapseChange = (keys: string | string[]) => {
    const newKeys = Array.isArray(keys) ? keys : [keys];
    setActiveCollapseKeys(newKeys);

    const latestKey = newKeys.length > 0 ? newKeys[newKeys.length - 1] : null;

    if (latestKey) {
      const plotId = Number(latestKey);
      const plotToFetch = forestPlots.find(p => p.id === plotId);
      
      highlightPlotOnMap(plotId);

      // Fetch personnel if not already fetched
      if (plotToFetch && !plotToFetch.personnel && !plotToFetch.personnelLoading) {
        setForestPlots(plots => plots.map(p => p.id === plotId ? { ...p, personnelLoading: true } : p));
        fetchPersonnel({ id: plotToFetch.id, quartelName: plotToFetch.name }, {
          onSuccess: (data: any) => {
            setForestPlots(plots => plots.map(p => p.id === plotId ? { ...p, personnel: data.data.users, personnelLoading: false } : p));
          },
          onError: () => {
            setForestPlots(plots => plots.map(p => p.id === plotId ? { ...p, personnelLoading: false } : p));
          }
        });
      }
    } else {
      highlightPlotOnMap(null);
    }
  };


  // 添加新林班（实际环境中应打开绘制工具）
  const addNewPlot = () => {
    alert('在实际应用中，此处应开启地图绘制工具来绘制新林班');
  };

  return (
    <div className="tab-content-panel">
      <div className="tab-header" style={{ padding: '8px 0', borderBottom: '1px solid #f0f0f0' }}>
        <Search
          placeholder="搜索林班名称"
          onChange={e => setSearchTerm(e.target.value)}
          style={{ width: '100%' }}
        />
      </div>
      <div className="tab-body">
        {linbanLoading ? (
          <Spin style={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)' }} />
        ) : (
          <Collapse activeKey={activeCollapseKeys} onChange={handleCollapseChange} ghost>
            {filteredForestPlots.map(plot => (
              <Panel 
                key={plot.id} 
                header={`${plot.name} ${plot.personnel ? `(${plot.personnel.length})` : ''}`}
              >
                {plot.personnelLoading ? (
                  <Spin size="small" style={{ display: 'block', margin: 'auto' }}/>
                ) : (
                  <List
                    itemLayout="horizontal"
                    dataSource={plot.personnel}
                    renderItem={(user: any) => (
                      <List.Item>
                        <List.Item.Meta
                          avatar={<Avatar icon={<UserOutlined />} />}
                          title={user.userName}
                          description={`电话: ${user.phoneNumber ?? 'N/A'}`}
                        />
                      </List.Item>
                    )}
                  />
                )}
              </Panel>
            ))}
          </Collapse>
        )}
      </div>
    </div>
  );
};

export default ForestPlotPanel; 
import { <PERSON>lt<PERSON>, Button } from 'antd';
import { BorderOutlined, GatewayOutlined, EnvironmentOutlined, ColumnWidthOutlined, ExpandOutlined, DeleteOutlined } from '@ant-design/icons';
import { FC, useEffect, useState, useRef } from 'react';
import { Map as OlMap, Overlay } from 'ol';
import Draw from 'ol/interaction/Draw';
import VectorSource from 'ol/source/Vector';
import VectorLayer from 'ol/layer/Vector';
import { Point, LineString, Polygon } from 'ol/geom';
import { Style, Fill, Stroke, Circle as CircleStyle } from 'ol/style';
import emitter, { ShowMapToolbarParams } from '@/utils/events';
import { getArea, getLength } from 'ol/sphere';
import { unByKey } from 'ol/Observable';
import Interaction from 'ol/interaction/Interaction';
import DoubleClickZoom from 'ol/interaction/DoubleClickZoom';

interface MapToolbarProps {
  map: OlMap | null;
  position?: 'top' | 'map';  // 新增属性，指定工具栏位置
  style?: React.CSSProperties;  // 新增样式属性
}

// 工具栏按钮配置
export const tools = [
  { id: 'polygon', title: '林班区域绘制', icon: <BorderOutlined /> },
  { id: 'linestring', title: '折线绘制', icon: <GatewayOutlined /> },
  { id: 'point', title: '添加标记', icon: <EnvironmentOutlined /> },
  { id: 'measure-distance', title: '距离量测', icon: <ColumnWidthOutlined /> },
  { id: 'measure-area', title: '面积量测', icon: <ExpandOutlined /> },
  { id: 'clear-measurement', title: '清除量算', icon: <DeleteOutlined /> },
];

const MapToolbar: FC<MapToolbarProps> = ({ map, position = 'map', style = {} }) => {
  // 使用useRef存储当前活动的绘制交互，确保在事件处理函数中能获取最新值
  const activeInteractionRef = useRef<Draw | null>(null);
  // 使用useRef存储当前是否正在绘制，避免首次点击地图触发点击外部隐藏
  const drawingActiveRef = useRef(false);
  // 添加状态控制工具栏显示
  const [visible, setVisible] = useState(position === 'top'); // 如果是顶部位置，则默认显示
  // 记录是否是标注模式
  const [isAnnotation, setIsAnnotation] = useState(false);
  // 记录当前选择的工具类型
  const [selectedTool, setSelectedTool] = useState<string | null>(null);
  // 使用ref存储工具栏DOM元素引用
  const toolbarRef = useRef<HTMLDivElement>(null);
  const measureTooltipElement = useRef<HTMLDivElement | null>(null);
  const measureTooltip = useRef<Overlay | null>(null);
  // 保存双击交互的ref
  const savedDblClickInteractionsRef = useRef<Interaction[]>([]);
  
  // 确保在组件加载时就禁用双击放大
  useEffect(() => {
    if (map) {
      // 找出并移除所有双击放大交互
      const dblClickInteractions = map.getInteractions().getArray()
        .filter(interaction => interaction instanceof DoubleClickZoom);
      
      dblClickInteractions.forEach(interaction => {
        map.removeInteraction(interaction);
      });
      
      // 添加全局双击事件拦截器
      const mapElement = map.getTargetElement();
      if (mapElement) {
        // 使用更高优先级的事件监听器
        mapElement.addEventListener('dblclick', preventDblClickZoom, { capture: true });
      }
      
      return () => {
        if (mapElement) {
          mapElement.removeEventListener('dblclick', preventDblClickZoom, { capture: true });
        }
        // 不需要恢复双击交互，因为我们希望全局禁用它
      };
    }
  }, [map]);
  
  // 监听显示地图工具栏事件
  useEffect(() => {
    const handleShowMapToolbar = (params: ShowMapToolbarParams) => {
      console.log('显示地图工具栏', params);
      
      // 如果是顶部位置，不需要切换可见性
      if (position !== 'top') {
        setVisible(true);
      }
      
      setIsAnnotation(Boolean(params.isAnnotation));
      
      if (params.type) {
        // 确保type映射到正确的工具ID
        let toolType = params.type;
        // 如果是marker，转换为point (UI上的工具ID是point)
        if (toolType === 'marker') {
          toolType = 'point';
        }
        setSelectedTool(toolType);
        
        // 如果传入了具体类型，直接开始绘制
        setTimeout(() => {
          startDrawing(toolType);
        }, 100);
      }
    };

    emitter.on("SHOW_MAP_TOOLBAR", handleShowMapToolbar);
    
    return () => {
      emitter.off("SHOW_MAP_TOOLBAR", handleShowMapToolbar);
    };
  }, [position]);

  // 单独处理点击事件监听，确保工具栏显示后才添加 (仅地图模式需要)
  useEffect(() => {
    // 如果在顶部，不需要处理外部点击隐藏
    if (position === 'top') return;
    
    // 处理全局点击事件，判断是否点击在工具栏外部
    const handleClickOutside = (e: MouseEvent) => {
      // 如果工具栏不可见，不处理
      if (!visible) return;
      // 如果正在绘制，跳过隐藏
      if (drawingActiveRef.current) return;
      // 如果工具栏元素不存在，不处理
      if (!toolbarRef.current) return;
      
      // 检查点击是否在工具栏之外
      if (!toolbarRef.current.contains(e.target as Node)) {
        console.log('点击在工具栏外部，隐藏工具栏');
        setVisible(false);
        
        // 移除绘制交互
        if (activeInteractionRef.current && map) {
          map.removeInteraction(activeInteractionRef.current);
          activeInteractionRef.current = null;
        }
      }
    };

    // 只有当工具栏可见时才添加点击事件监听
    if (visible) {
      // 延迟添加事件监听，避免立即触发
      const timer = setTimeout(() => {
        document.addEventListener('click', handleClickOutside);
      }, 100);
      
      return () => {
        clearTimeout(timer);
        document.removeEventListener('click', handleClickOutside);
      };
    }
  }, [visible, map, position]);

  // 阻止双击放大的事件处理函数
  const preventDblClickZoom = (e: MouseEvent) => {
    console.log('阻止双击放大');
    e.stopPropagation();
    e.preventDefault();
    // 阻止事件冒泡
    if (e.stopImmediatePropagation) {
      e.stopImmediatePropagation();
    }
    return false;
  };
  
  // 彻底禁用地图的双击放大交互
  const disableMapDblClickZoom = () => {
    if (!map) return;
    
    // 1. 移除所有双击放大交互
    const interactions = map.getInteractions().getArray();
    const dblClickInteractions = interactions.filter(interaction => 
      interaction instanceof DoubleClickZoom
    );
    
    // 保存它们以便之后恢复（虽然我们可能不会恢复它们）
    savedDblClickInteractionsRef.current = dblClickInteractions;
    
    // 从地图中移除
    dblClickInteractions.forEach(interaction => {
      map.removeInteraction(interaction);
    });
    
    // 2. 在地图容器上直接阻止双击事件 - 使用捕获模式
    const mapElement = map.getTargetElement();
    if (mapElement) {
      // 移除任何现有的监听器
      mapElement.removeEventListener('dblclick', preventDblClickZoom, { capture: true });
      // 添加新的监听器
      mapElement.addEventListener('dblclick', preventDblClickZoom, { capture: true });
      
      // 3. 添加额外的事件监听器
      const disableEvents = (e: MouseEvent) => {
        // 跟踪连续点击
        const now = Date.now();
        if (lastClickTime.current && (now - lastClickTime.current < 500)) {
          // 这是一次快速的连续点击（可能是双击）
          console.log('拦截潜在双击');
          e.stopPropagation();
          e.preventDefault();
          if (e.stopImmediatePropagation) {
            e.stopImmediatePropagation();
          }
          lastClickTime.current = 0; // 重置计时器
          return false;
        }
        lastClickTime.current = now;
      };
      
      // 移除现有监听器
      mapElement.removeEventListener('click', disableEvents, { capture: true });
      // 添加监听器
      mapElement.addEventListener('click', disableEvents, { capture: true });
    }
    
    // 4. 使用接管mousedown/mouseup事件的方式
    if (!mouseEventHandlerAdded.current) {
      document.addEventListener('mousedown', trackMouseDown, { capture: true });
      document.addEventListener('mouseup', trackMouseUp, { capture: true });
      mouseEventHandlerAdded.current = true;
    }
  };
  
  // 跟踪连续点击的引用
  const lastClickTime = useRef<number>(0);
  const mouseEventHandlerAdded = useRef<boolean>(false);
  const clickCount = useRef<number>(0);
  const clickTimer = useRef<any>(null);
  
  // 跟踪mousedown事件
  const trackMouseDown = (e: MouseEvent) => {
    if (drawingActiveRef.current) {
      clickCount.current++;
      
      if (clickCount.current === 2) {
        // 这可能是双击的第二次点击
        console.log('检测到潜在双击 (mousedown)');
        e.stopPropagation();
        e.preventDefault();
        if (e.stopImmediatePropagation) {
          e.stopImmediatePropagation();
        }
      }
      
      // 重置计数器
      if (clickTimer.current) {
        clearTimeout(clickTimer.current);
      }
      
      clickTimer.current = setTimeout(() => {
        clickCount.current = 0;
      }, 500);
    }
  };
  
  // 跟踪mouseup事件
  const trackMouseUp = (e: MouseEvent) => {
    if (drawingActiveRef.current && clickCount.current === 2) {
      // 这可能是双击的第二次释放
      console.log('检测到潜在双击 (mouseup)');
      e.stopPropagation();
      e.preventDefault();
      if (e.stopImmediatePropagation) {
        e.stopImmediatePropagation();
      }
    }
  };

  // 恢复地图的双击放大交互 - 如果需要的话
  const enableMapDblClickZoom = () => {
    // 此处我们不做任何事情，因为我们希望地图始终禁用双击放大
    // 这样用户体验会更一致
  };

  // 在组件卸载时清理
  useEffect(() => {
    return () => {
      if (mouseEventHandlerAdded.current) {
        document.removeEventListener('mousedown', trackMouseDown, { capture: true });
        document.removeEventListener('mouseup', trackMouseUp, { capture: true });
      }
    };
  }, []);

  const clearInteractions = () => {
    if (map) {
      // 如果当前是测量工具，发送测量模式结束事件
      if (selectedTool?.startsWith('measure-')) {
        emitter.emit("MEASURE_MODE_CHANGED", { isActive: false });
      }
      
      // 如果正在绘制（非测量工具），发送绘制模式结束事件
      if (drawingActiveRef.current && !selectedTool?.startsWith('measure-')) {
        console.log('clearInteractions发送绘制模式结束事件');
        emitter.emit("DRAWING_MODE_CHANGED", { isActive: false });
        drawingActiveRef.current = false;
      }
      
      if (activeInteractionRef.current) {
        map.removeInteraction(activeInteractionRef.current);
        activeInteractionRef.current = null;
      }
      if (measureTooltip.current) {
        map.removeOverlay(measureTooltip.current);
        measureTooltip.current = null;
      }
    }
    
    setSelectedTool(null);
  };
  
  const startMeasure = (type: 'distance' | 'area') => {
    if (!map) return;
  
    clearInteractions(); // Clear previous interactions and tooltips
    setSelectedTool(`measure-${type}`);
    drawingActiveRef.current = true;
    
    // 发送测量模式激活事件
    console.log('MapToolbar发送测量模式激活事件:', { isActive: true, type });
    emitter.emit("MEASURE_MODE_CHANGED", { isActive: true, type });
  
    const source = new VectorSource();
    const vector = new VectorLayer({
      source: source,
      style: new Style({
        fill: new Fill({
          color: 'rgba(255, 255, 255, 0.2)'
        }),
        stroke: new Stroke({
          color: '#ffcc33',
          width: 2
        }),
        image: new CircleStyle({
          radius: 7,
          fill: new Fill({
            color: '#ffcc33'
          })
        })
      }),
      // 添加标记，表明这是量算图层
      properties: {
        'isMeasureLayer': true,
        'name': `measure-${type}`
      }
    });
    map.addLayer(vector);
  
    let sketch: any;
    let listener: any;
    
    // 在开始量算时彻底禁用地图默认的双击放大功能
    disableMapDblClickZoom();
  
    const draw = new Draw({
      source,
      type: type === 'distance' ? 'LineString' : 'Polygon',
      // 添加鼠标右键过滤条件，只允许左键量算
      condition: function(mapBrowserEvent) {
        return mapBrowserEvent.originalEvent instanceof MouseEvent && 
               mapBrowserEvent.originalEvent.button === 0; // 0表示鼠标左键
      },
      // 配置双击结束绘制而不是放大地图
      freehandCondition: () => false,  // 禁用自由绘制模式
      stopClick: false  // 不阻止单击事件
    });
    map.addInteraction(draw);
    activeInteractionRef.current = draw;
  
    // 添加双击监听器，在双击时立即发送测量结束事件
    const handleDoubleClick = (evt: any) => {
      console.log('🔥 MapToolbar检测到双击，立即发送测量结束事件');
      emitter.emit("MEASURE_MODE_CHANGED", { isActive: false });
    };
    
    // 监听地图的双击事件
    map.on('dblclick', handleDoubleClick);
    
    // 在交互结束时移除监听器
    const originalRemoveInteraction = () => {
      map.un('dblclick', handleDoubleClick);
      if (activeInteractionRef.current) {
        map.removeInteraction(activeInteractionRef.current);
        activeInteractionRef.current = null;
      }
    };
  
    createMeasureTooltip();
  
    // 绘制开始时禁用双击放大
    draw.on('drawstart', (evt) => {
      disableMapDblClickZoom();
      
      sketch = evt.feature;
  
      let tooltipCoord = sketch.getGeometry()?.getLastCoordinate();
  
      listener = sketch.getGeometry().on('change', (evt: any) => {
        const geom = evt.target;
        let output;
        if (geom instanceof Polygon) {
          output = formatArea(getArea(geom));
          tooltipCoord = geom.getInteriorPoint().getCoordinates();
        } else if (geom instanceof LineString) {
          output = formatLength(getLength(geom));
          tooltipCoord = geom.getLastCoordinate();
        }
        if (measureTooltipElement.current) {
          measureTooltipElement.current.innerHTML = output || '';
        }
        if (measureTooltip.current) {
          measureTooltip.current.setPosition(tooltipCoord);
        }
      });
    });
  
    draw.on('drawend', (evt) => {
      // 立即发送测量模式结束事件，抢在任何点击事件之前
      console.log('MapToolbar立即发送测量模式结束事件');
      emitter.emit("MEASURE_MODE_CHANGED", { isActive: false });
      
      // 为测量要素添加标识，避免被点击选中
      evt.feature.set('isMeasureFeature', true);
      evt.feature.set('measureType', type);
      
      if (measureTooltipElement.current) {
        measureTooltipElement.current.className = 'ol-tooltip ol-tooltip-static';
      }
      if (measureTooltip.current) {
        measureTooltip.current.setOffset([0, -7]);
      }
      sketch = null;
      unByKey(listener);
      
      // 恢复地图双击交互
      enableMapDblClickZoom();
      
      // 移除双击监听器并清理交互
      originalRemoveInteraction();
      
      drawingActiveRef.current = false;
      setSelectedTool(null);
    });
  }

  function createMeasureTooltip() {
    if (measureTooltipElement.current && measureTooltipElement.current.parentNode) {
      measureTooltipElement.current.parentNode.removeChild(measureTooltipElement.current);
    }
    measureTooltipElement.current = document.createElement('div');
    measureTooltipElement.current.className = 'ol-tooltip ol-tooltip-measure';
    measureTooltip.current = new Overlay({
      element: measureTooltipElement.current,
      offset: [0, -15],
      positioning: 'bottom-center'
    });
    map?.addOverlay(measureTooltip.current);
  }
  
  const formatLength = function (length: number) {
    let output;
    if (length > 100) {
      output = (Math.round(length / 1000 * 100) / 100) + ' ' + 'km';
    } else {
      output = (Math.round(length * 100) / 100) + ' ' + 'm';
    }
    return output;
  };

  const formatArea = function (area: number) {
    let output;
    if (area > 10000) {
      output = (Math.round(area / 1000000 * 100) / 100) + ' ' + 'km<sup>2</sup>';
    } else {
      output = (Math.round(area * 100) / 100) + ' ' + 'm<sup>2</sup>';
    }
    return output;
  };

  const startDrawing = (shape: string) => {
    if (!map) {
      console.log('地图不存在，无法开始绘制');
      return;
    }

    if (shape === 'measure-distance') {
      startMeasure('distance');
      return;
    }
    if (shape === 'measure-area') {
      startMeasure('area');
      return;
    }
    if (shape === 'clear-measurement') {
      clearMeasurements();
      return;
    }

    console.log(`开始绘制: ${shape}`);
    setSelectedTool(shape);
    // 标记绘制活动开始
    drawingActiveRef.current = true;
    
    // 发送绘制模式激活事件
    console.log('MapToolbar发送绘制模式激活事件:', { isActive: true, type: shape });
    emitter.emit("DRAWING_MODE_CHANGED", { isActive: true, type: shape });

    // 移除所有活动的绘制交互
    if (activeInteractionRef.current) {
      map.removeInteraction(activeInteractionRef.current);
      activeInteractionRef.current = null;
    }

    // 查找绘制图层
    const drawLayer = map.getLayers().getArray().find(layer => 
      layer.get('name') === '绘制图层'
    ) as VectorLayer<VectorSource>;
    
    if (!drawLayer) {
      console.log('找不到绘制图层');
      return;
    }
    
    const drawSource = drawLayer.getSource();
    if (!drawSource) {
      console.log('找不到绘制图层源');
      return;
    }

    // 创建新的绘制交互
    let type: 'Point' | 'LineString' | 'Polygon';
    
    switch (shape) {
      case 'polygon':
        type = 'Polygon';
        break;
      case 'linestring':
        type = 'LineString';
        break;
      case 'point':
      case 'marker':
        type = 'Point';
        break;
      default:
        console.log('不支持的绘制类型:', shape);
        return;
    }
    
    const drawInteraction = new Draw({
      source: drawSource,
      type: type,
      // 添加鼠标右键过滤条件，只允许左键绘制
      condition: function(mapBrowserEvent) {
        // 检查是否为鼠标事件，并且只允许左键
        return mapBrowserEvent.originalEvent instanceof MouseEvent && 
               mapBrowserEvent.originalEvent.button === 0; // 0表示鼠标左键
      },
      // 启用双击结束绘制功能
      freehandCondition: () => false,  // 禁用自由绘制模式
      stopClick: false,  // 不阻止单击事件
      style: new Style({  // 自定义绘制样式
        fill: new Fill({
          color: 'rgba(255, 255, 255, 0.2)'
        }),
        stroke: new Stroke({
          color: '#ff0000',
          width: 2
        }),
        image: new CircleStyle({
          radius: 7,
          fill: new Fill({
            color: '#ff0000'
          })
        })
      })
    });
    
    // 绘制开始前禁用双击放大
    disableMapDblClickZoom();
    
    // 绘制开始时禁用双击放大
    drawInteraction.on('drawstart', () => {
      disableMapDblClickZoom();
    });
    
    // 绘制结束后处理
    drawInteraction.on('drawend', (evt) => {
      console.log('绘制完成');
      
      // 恢复地图双击交互
      enableMapDblClickZoom();
      
      // 根据绘制类型设置默认样式
      const feature = evt.feature;
      const geometry = feature.getGeometry();
      if (!geometry) return;
      
      const geomType = geometry.getType();
      
      let defaultStyle: { [key: string]: any } = {
        strokeColor: '#ff0000',
        strokeWeight: 2,
        strokeOpacity: 1,
        strokeStyle: 'solid'
      };
      
      // 如果是多边形，添加填充样式
      if (geomType === 'Polygon') {
        defaultStyle = {
          ...defaultStyle,
          fillColor: '#ffff00',
          fillOpacity: 0.5,
          label: '林班区域'
        };
      } else if (geomType === 'LineString') {
        defaultStyle = {
          ...defaultStyle,
          label: '折线'
        };
      } else if (geomType === 'Point') {
        defaultStyle = {
          ...defaultStyle,
          label: '标记点'
        };
      }
      
      // 设置自定义信息
      feature.set('customInfo', defaultStyle);

      // 绘制完成后，自动禁用绘制工具（不管是否为标注模式）
      map.removeInteraction(drawInteraction);
      activeInteractionRef.current = null;
        setSelectedTool(null);
        drawingActiveRef.current = false;
        
        // 发送绘制模式结束事件
        console.log('MapToolbar发送绘制模式结束事件');
        emitter.emit("DRAWING_MODE_CHANGED", { isActive: false });
      
      // 发送绘制完成事件 - 但排除测量工具
      if (!shape?.startsWith('measure-')) {
        emitter.emit("DRAW_COMPLETE", { 
          feature: evt.feature,
          isAnnotation: isAnnotation
        });
        console.log('已发送绘制完成事件');
      } else {
        console.log('测量工具完成，不发送绘制完成事件');
      }
    });
    
    map.addInteraction(drawInteraction);
    activeInteractionRef.current = drawInteraction;
    console.log('绘制交互已添加到地图');
  };
  
  // 清除所有量算结果
  const clearMeasurements = () => {
    if (!map) return;
    
    console.log('清除所有量算结果');
    
    // 查找并清除量算图层
    const layers = map.getLayers().getArray();
    const measureLayers = layers.filter(layer => {
      // 识别量算图层 - 这里假设量算图层有特定的属性或名称
      // 您可能需要根据实际情况调整此条件
      return layer.get('isMeasureLayer') === true || 
             (typeof layer.get('name') === 'string' && 
              (layer.get('name').includes('measure') || layer.get('name').includes('量算')));
    });
    
    // 从地图中移除量算图层
    measureLayers.forEach(layer => {
      map.removeLayer(layer);
    });
    
    // 清除量算提示框
    const overlays = map.getOverlays().getArray();
    const measureTooltips = overlays.filter(overlay => {
      const element = overlay.getElement();
      return element && (
        element.className.includes('ol-tooltip-measure') || 
        element.className.includes('ol-tooltip-static')
      );
    });
    
    measureTooltips.forEach(overlay => {
      map.removeOverlay(overlay);
    });
    
    setSelectedTool(null);
    
    // 发送测量模式结束事件
    emitter.emit("MEASURE_MODE_CHANGED", { isActive: false });
  };
  
  // 如果不可见则不渲染（仅适用于地图模式，顶部模式始终渲染）
  if (!visible) return null;
  
  // 根据位置类型确定样式
  const containerStyle = position === 'map' ? {
    position: 'absolute' as const,
    top: '90px',
    left: '50%',
    transform: 'translateX(-50%)',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    padding: '5px',
    borderRadius: '8px',
    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
    zIndex: 500,
    display: 'flex',
    gap: '5px',
    ...style
  } : {
    display: 'flex',
    gap: '5px',
    ...style
  };
  
  return (
    <div 
      ref={toolbarRef}
      className={`map-toolbar ${position === 'top' ? 'top-toolbar' : ''}`}
      style={containerStyle}
      // 添加点击事件处理，阻止事件冒泡（仅用于地图模式）
      onClick={position === 'map' ? (e) => {
        e.stopPropagation();
        console.log('工具栏被点击');
      } : undefined}
    >
      {tools.map(tool => (
        <Tooltip key={tool.id} title={tool.title} placement={position === 'top' ? 'bottom' : 'bottom'}>
          <Button 
            className={position === 'top' ? 'border-0 bg-transparent' : ''}
            icon={tool.icon} 
            onClick={(e) => {
              // 阻止事件冒泡
              e.stopPropagation();
              e.preventDefault();
              console.log(`工具按钮被点击: ${tool.id}`);
              clearInteractions(); // Clear any previous interactions before starting new one
              startDrawing(tool.id);
            }}
            type={selectedTool === tool.id ? 'primary' : 'default'}
            style={position === 'top' ? {
              color: selectedTool === tool.id ? '#ffffff' : 'rgba(255,255,255,0.8)',
              backgroundColor: selectedTool === tool.id ? '#3370ff' : 'transparent'
            } : undefined}
          />
        </Tooltip>
      ))}
    </div>
  );
};

export default MapToolbar; 
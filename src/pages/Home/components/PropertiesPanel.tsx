import React, { useState, useEffect, FC } from 'react';
import { Card, Form, Input, Slider, Button, ColorPicker, Spin, Select as AntdSelect, InputNumber, Popconfirm, Tooltip } from 'antd';
import type { Color } from 'antd/es/color-picker';
import { CloseOutlined, EditOutlined, SaveOutlined, DeleteOutlined, ShareAltOutlined } from '@ant-design/icons';
import Feature from 'ol/Feature';
import { Fill, Stroke, Style } from 'ol/style';
import { useGetAllLinban } from '@/api/linban';
import { useUserStore } from '@/store/user';

// 内联样式，确保Select组件文字颜色正确
const selectStyles = `
.properties-select .ant-select-selector {
    color: #000000 !important;
}
.properties-select .ant-select-selection-item {
    color: #000000 !important;
}
.properties-select .ant-select-selection-placeholder {
    color: #999999 !important;
}
.ant-select-dropdown .ant-select-item-option-content {
    color: #000000 !important;
}
`;

interface PropertiesPanelProps {
    feature: Feature | any | null;
    onClose: () => void;
    onChange: (feature: Feature | null, newProperties: any) => void;
    onSaveDrawing?: (values: any) => void;
    onDeleteDrawing?: () => void;
    loading?: boolean;
    labelDetail?: any; // 标注详情数据
    isLabelDetailLoading?: boolean; // 标注详情加载状态
    onEdit?: () => void; // 进入编辑模式的回调
    onSave?: (values: any) => void; // 保存编辑的回调
    isEditMode?: boolean; // 是否处于编辑模式
    editLoading?: boolean; // 编辑保存加载状态
    onDeleteLabel?: () => void; // 删除标注的回调
    deleteLoading?: boolean; // 删除标注加载状态
    onShareLabel?: () => void; // 分享标注的回调
}

const PropertiesPanel: FC<PropertiesPanelProps> = ({ 
    feature, 
    onClose, 
    onChange, 
    onSaveDrawing, 
    onDeleteDrawing, 
    loading = false, 
    labelDetail, 
    isLabelDetailLoading = false,
    onEdit,
    onSave,
    isEditMode = false,
    editLoading = false,
    onDeleteLabel,
    deleteLoading = false,
    onShareLabel
}) => {
    const [form] = Form.useForm();
    const [properties, setProperties] = useState({
        name: '未命名',
        fillColor: '#3388ff',
        opacity: 0.2,
        strokeColor: '#3388ff',
        strokeWeight: 2,
        strokeOpacity: 1,
        strokeStyle: 'solid',
    });
    const userId = useUserStore((state) => state.linBanUser.id);
    const { data: linbanData, isLoading: linbanLoading } = useGetAllLinban(userId ? String(userId) : '');
    const isLoading = linbanLoading || loading || isLabelDetailLoading || editLoading;
    const hasLabelId = !!feature?.get?.('labelId');

    useEffect(() => {
        if (feature) {
            let initialProps: any = {
                name: '未命名',
                fillColor: '#3388ff',
                opacity: 0.2,
                strokeColor: '#3388ff',
                strokeWeight: 2,
                strokeOpacity: 1,
                strokeStyle: 'solid',
            };

            // 判断 feature 类型
            if (typeof feature.get === 'function') {
                const customInfo = feature.get('customInfo');
                if (customInfo) {
                    initialProps = { ...initialProps, ...customInfo };
                } else {
                    // OpenLayers Feature 对象
                    initialProps.name = feature.get('name') || '未命名';
                    
                    const style = feature.getStyle();
                    if (style) {
                        let fillStyle: Fill | null = null;
                        if (Array.isArray(style)) {
                            if (style.length > 0) fillStyle = style[0].getFill();
                        } else if (typeof style !== 'function') {
                            fillStyle = style.getFill();
                        }
                        
                        if (fillStyle) {
                            const fillColor = fillStyle.getColor();
                            if (typeof fillColor === 'string') {
                                initialProps.fillColor = fillColor;
                            } else if (Array.isArray(fillColor) && fillColor.length >= 4) {
                                initialProps.opacity = fillColor[3];
                                initialProps.fillColor = `rgb(${fillColor[0]},${fillColor[1]},${fillColor[2]})`;
                            }
                        }
                    }
                }
            } else {
                // 普通 GeoJSON 对象
                const props = feature.properties || {};
                initialProps = {
                    ...initialProps,
                    name: props.title || props.name || '未命名',
                    fillColor: props.color || '#3388ff',
                    opacity: props.opacity || 0.2, // 假设GeoJSON里可能有opacity
                };
            }
            
            setProperties(initialProps);
            form.setFieldsValue(initialProps);
        }
    }, [feature, form]);

    // 监听标注详情变化
    useEffect(() => {
        if (labelDetail) {
            const detailProps: any = {
                name: labelDetail.labelName || '未命名',
                remark: labelDetail.labelRemark || '',
                quartelId: labelDetail.quartelId,
                type: labelDetail.type || '',
            };
            
            // 如果有JSON数据，解析出更多属性
            if (labelDetail.dataJson) {
                try {
                    let dataJson = labelDetail.dataJson;
                    if (typeof dataJson === 'string') {
                        dataJson = JSON.parse(dataJson);
                    }
                    
                    // 从dataJson中提取属性
                    if (dataJson.properties) {
                        const props = dataJson.properties;
                        
                        // 基本属性
                        detailProps.area = props.area || '';
                        
                        // 🔥 完整的样式属性
                        detailProps.fillColor = props.fillColor || props.color || '#3388ff';
                        detailProps.opacity = props.fillOpacity !== undefined ? props.fillOpacity : 0.2;
                        detailProps.strokeColor = props.strokeColor || props.color || '#3388ff';
                        detailProps.strokeWeight = props.strokeWeight || props.lineWidth || 2;
                        detailProps.strokeOpacity = props.strokeOpacity !== undefined ? props.strokeOpacity : 1;
                        detailProps.strokeStyle = props.strokeStyle || 'solid';
                        
                        console.log('从标注详情中加载的样式数据:', detailProps);
                    }
                } catch (error) {
                    console.error('解析标注详情JSON失败:', error);
                }
            }
            
            // 更新表单数据
            form.setFieldsValue(detailProps);
            setProperties(prev => ({
                ...prev,
                ...detailProps
            }));
        }
    }, [labelDetail, form]);

    const handleValuesChange = (changedValues: any, allValues: any) => {
        // 转换 Color 对象为字符串
        const newValues = {
            ...allValues,
            fillColor: allValues.fillColor?.toHexString?.() ?? allValues.fillColor,
            strokeColor: allValues.strokeColor?.toHexString?.() ?? allValues.strokeColor,
        };
        setProperties(newValues);
        onChange(feature, newValues);
    };

    const handleEdit = () => {
        if (onEdit) {
            onEdit();
        }
    };

    const handleSave = () => {
        if (onSave) {
            onSave(form.getFieldsValue());
        }
    };

    const renderExtraButtons = () => (
        <>
            {hasLabelId && (
                <>
                    {onShareLabel && (
                        <Tooltip title="分享标注">
                            <Button 
                                type="text" 
                                icon={<ShareAltOutlined />} 
                                style={{ color: '#1890ff', marginRight: 8 }}
                                onClick={onShareLabel}
                            />
                        </Tooltip>
                    )}
                    <Tooltip title="删除标注">
                        <Popconfirm
                            title="删除标注"
                            description="确定要删除这个标注吗？此操作不可恢复。"
                            onConfirm={onDeleteLabel}
                            okText="确定"
                            cancelText="取消"
                            placement="bottomRight"
                            okButtonProps={{ danger: true, loading: deleteLoading }}
                        >
                            <Button 
                                type="text" 
                                icon={<DeleteOutlined />} 
                                style={{ color: '#ff4d4f', marginRight: 8 }}
                                loading={deleteLoading}
                            />
                        </Popconfirm>
                    </Tooltip>
                    {isEditMode ? (
                        <Button 
                            type="primary" 
                            icon={<SaveOutlined />} 
                            onClick={handleSave}
                            style={{ marginRight: 8 }}
                            loading={editLoading}
                        >
                            保存
                        </Button>
                    ) : (
                        <Button 
                            type="default" 
                            icon={<EditOutlined />} 
                            onClick={handleEdit}
                            style={{ marginRight: 8 }}
                        >
                            编辑
                        </Button>
                    )}
                </>
            )}
            <Button type="text" icon={<CloseOutlined />} onClick={onClose} />
        </>
    );

    return (
        <>
            <style dangerouslySetInnerHTML={{ __html: selectStyles }} />
            <Card
                title="属性信息"
                size="small"
                extra={renderExtraButtons()}
                bodyStyle={{ overflowY: 'auto', padding: '12px 24px' }}
                style={{
                    position: 'absolute',
                    right: '10px',
                    top: '60px',
                    width: 300,
                    height: '600px',
                    zIndex: 500,
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                    maxHeight: 'calc(100vh - 60px)',
                    display: 'flex',
                    flexDirection: 'column',
                }}
            >
            <Spin spinning={isLoading} tip="加载中...">
            <Form
                form={form}
                layout="vertical"
                onValuesChange={handleValuesChange}
                onFinish={isEditMode ? handleSave : onSaveDrawing}
                initialValues={properties}
            >
                {/* 基本信息 */}
                <Form.Item label="名称" name="name" style={{ marginBottom: 8 }}>
                    <Input disabled={!isEditMode && hasLabelId} />
                </Form.Item>
                <Form.Item label="类型" name="type" style={{ marginBottom: 8 }}>
                    <Input disabled={!isEditMode && hasLabelId} />
                </Form.Item>
                <Form.Item label="面积" name="area" style={{ marginBottom: 8 }}>
                    <Input addonAfter="公顷" disabled={!isEditMode && hasLabelId} />
                </Form.Item>
                <Form.Item label="所属林班" name="quartelId" style={{ marginBottom: 8 }}>
                    <AntdSelect 
                        placeholder="选择所属林班" 
                        disabled={!isEditMode && hasLabelId}
                        style={{
                            border: '1px solid #d9d9d9',
                            borderRadius: '4px',
                            color: '#000000 !important'
                        }}
                        dropdownStyle={{
                            color: '#000000'
                        }}
                        className="properties-select"
                    >
                        {linbanData?.data?.map((linban: any) => (
                            <AntdSelect.Option key={linban.id} value={linban.id} style={{ color: '#000000' }}>
                                {linban.quartelName}
                            </AntdSelect.Option>
                        ))}
                    </AntdSelect>
                </Form.Item>

                {/* 样式设置 */}
                <div style={{ margin: '12px 0 8px', fontWeight: 'bold' }}>样式设置</div>
                <Form.Item label="填充颜色" name="fillColor" style={{ marginBottom: 8 }}>
                    <ColorPicker showText disabled={!isEditMode && hasLabelId} />
                </Form.Item>
                <Form.Item label="填充透明度" name="opacity" style={{ marginBottom: 8 }}>
                    <Slider min={0} max={1} step={0.05} disabled={!isEditMode && hasLabelId} />
                </Form.Item>
                <Form.Item label="边线颜色" name="strokeColor" style={{ marginBottom: 8 }}>
                    <ColorPicker showText disabled={!isEditMode && hasLabelId} />
                </Form.Item>
                <Form.Item label="边线宽度" name="strokeWeight" style={{ marginBottom: 8 }}>
                    <InputNumber min={1} style={{ width: '100%' }} disabled={!isEditMode && hasLabelId} />
                </Form.Item>
                <Form.Item label="边线透明度" name="strokeOpacity" style={{ marginBottom: 8 }}>
                    <Slider min={0} max={1} step={0.05} disabled={!isEditMode && hasLabelId} />
                </Form.Item>
                <Form.Item label="边框样式" name="strokeStyle" style={{ marginBottom: 8 }}>
                    <AntdSelect 
                        disabled={!isEditMode && hasLabelId}
                        style={{
                            border: '1px solid #d9d9d9',
                            borderRadius: '4px',
                            color: '#000000 !important'
                        }}
                        dropdownStyle={{
                            color: '#000000'
                        }}
                        className="properties-select"
                    >
                        <AntdSelect.Option value="solid" style={{ color: '#000000' }}>实线</AntdSelect.Option>
                        <AntdSelect.Option value="dashed" style={{ color: '#000000' }}>虚线</AntdSelect.Option>
                    </AntdSelect>
                </Form.Item>

                {/* 备注信息 */}
                <Form.Item label="备注" name="remark" style={{ marginBottom: 8 }}>
                    <Input.TextArea rows={2} disabled={!isEditMode && hasLabelId} />
                </Form.Item>

                {/* 操作按钮 */}
                <Form.Item style={{ textAlign: 'right', marginTop: 16, marginBottom: 0 }}>
                    {!hasLabelId && onDeleteDrawing && (
                        <Button onClick={onDeleteDrawing} style={{ marginRight: 8 }} danger>
                            删除
                        </Button>
                    )}
                    {!hasLabelId && onSaveDrawing && (
                        <Button 
                            type="primary" 
                            htmlType="submit"
                            icon={<SaveOutlined />}
                            size="large"
                            style={{ minWidth: '120px' }}
                            loading={loading}
                        >
                            保存标注
                        </Button>
                    )}
                </Form.Item>
            </Form>
            </Spin>
        </Card>
        </>
    );
};

export default PropertiesPanel; 
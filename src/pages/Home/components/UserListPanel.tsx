import React, { useEffect, useState, useMemo } from 'react';
import { Collapse, List, Spin, Input, Avatar } from 'antd';
import { useGetLastPosition } from '@/api/linban';
import { useInView } from 'react-intersection-observer';
import { UserOutlined } from '@ant-design/icons';

const { Search } = Input;

interface Job {
  id: number;
  job_name: string;
  users: Array<{ id: number; name: string; grade: string; phone: string; job_id: string }>;
}

const { Panel } = Collapse;

interface UserListPanelProps {
  onUserSelect: (user: { id: number; name: string; grade: string; phone: string; job_id: string }, coordinate: { lon: number; lat: number }) => void;
}

const UserListItem: React.FC<{
  user: { id: number; name: string; grade: string; phone: string; job_id: string };
  onUserSelect: UserListPanelProps['onUserSelect'];
  isSelected: boolean;
}> = ({ user, onUserSelect, isSelected }) => {
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });
  const { data: positionData, isLoading } = useGetLastPosition(String(user.id), {
    enabled: inView,
  });

  const processedData = useMemo(() => {
    if (positionData?.code === 0 && positionData?.data) {
      const { data } = positionData;
      const lon = data.longitude;
      const lat = data.latitude;
      const position = (typeof lon === 'number' && typeof lat === 'number') ? { lon, lat } : null;

      return {
        name: data.userName || user.name,
        grade: data.grade || user.grade,
        phone: data.phoneNumber || user.phone,
        position,
      };
    }
    return {
      name: user.name,
      grade: user.grade,
      phone: user.phone,
      position: null,
    };
  }, [positionData, user]);

  return (
    <div ref={ref}>
      <List.Item
        onClick={() => {
          if (processedData.position) {
            onUserSelect(user, processedData.position);
          }
        }}
        style={{ 
          cursor: processedData.position ? 'pointer' : 'default', 
          minHeight: 70,
          backgroundColor: isSelected ? '#e6f7ff' : 'transparent',
          borderRadius: '4px',
          transition: 'background-color 0.3s',
        }}
      >
        <List.Item.Meta
          avatar={<Avatar icon={<UserOutlined />} />}
          title={processedData.name}
          description={
            <>
              <div> 电话: {processedData.phone} {isLoading && <span>坐标加载中...</span>}</div>
              {processedData.position && <span>坐标: {processedData.position.lon.toFixed(4)}, {processedData.position.lat.toFixed(4)}</span>}
              {!isLoading && !processedData.position && <span style={{ color: '#ccc' }}>坐标不可用</span>}
            </>
          }
        />
      </List.Item>
    </div>
  );
};

const UserListPanel: React.FC<UserListPanelProps> = ({ onUserSelect }) => {
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);

  useEffect(() => {
    setLoading(true);
    fetch('https://www.senfalinye.com/pounds/Gis.Index/getJobList', { method: 'POST' })
      .then(res => res.json())
      .then(data => {
        if (data.code === 1 && Array.isArray(data.data)) {
          setJobs(data.data);
        }
      })
      .catch(err => console.error('获取工种列表失败', err))
      .finally(() => setLoading(false));
  }, []);

  const filteredJobs = useMemo(() => {
    if (!searchTerm) {
      return jobs;
    }
    return jobs.map(job => {
      const filteredUsers = job.users.filter(user => 
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.phone.includes(searchTerm)
      );
      return { ...job, users: filteredUsers };
    }).filter(job => job.users.length > 0);
  }, [jobs, searchTerm]);

  const handleUserClick = (user: any, coordinate: { lon: number, lat: number }) => {
    setSelectedUserId(user.id);
    onUserSelect(user, coordinate);
  };

  if (loading) {
    return <Spin style={{ display: 'block', textAlign: 'center', marginTop: 20 }} />;
  }

  return (
    <div className="tab-content-panel">
      <div style={{ padding: '8px 0', borderBottom: '1px solid #f0f0f0' }}>
        <Search 
          placeholder="搜索姓名或电话" 
          onChange={e => setSearchTerm(e.target.value)} 
          style={{ width: '100%' }}
        />
      </div>
      <div className="tab-body">
        <Collapse defaultActiveKey={filteredJobs.map(j => j.id.toString())} ghost>
          {filteredJobs.map(job => (
            <Panel key={job.id} header={`${job.job_name} (${job.users.length})`}>
              <List
                style={{ padding: '0 8px' }}
                itemLayout="horizontal"
                dataSource={job.users}
                renderItem={user => (
                  <UserListItem 
                    user={user} 
                    onUserSelect={handleUserClick} 
                    isSelected={user.id === selectedUserId}
                  />
                )}
              />
            </Panel>
          ))}
        </Collapse>
      </div>
    </div>
  );
};

export default UserListPanel; 
import React, { useEffect, useState, useRef } from 'react';
import 'ol/ol.css';
import { Map as OlMap, View, Collection } from 'ol';
import TileLayer from 'ol/layer/Tile';
import OSM from 'ol/source/OSM';
import XYZ from 'ol/source/XYZ';
import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import { Point, LineString } from 'ol/geom';
import Feature from 'ol/Feature';
import { Fill, Stroke, Style, Circle as CircleStyle, Text } from 'ol/style';
import { fromLonLat, toLonLat } from 'ol/proj';
import MapToolbar from './components/MapToolbar';
import PropertiesPanel from './components/PropertiesPanel';
import Draw from 'ol/interaction/Draw';
import { Button, message } from 'antd';
import SidePanel from './components/SidePanel';
import UserInfoPanel from './components/UserInfoPanel';
import { getTrajectory, useAddFavorite, useCreateLabel, useGetAllLabels, useGetLabelById, useUpdateLabel, useDeleteLabel } from '@/api/linban';
import GeoJSON from 'ol/format/GeoJSON';
import { useUserStore } from '@/store/user';
import { useQueryClient } from 'react-query';
import emitter, { DrawCompleteParams, FilterByLinbanParams } from '@/utils/events';
import TileWMS from 'ol/source/TileWMS';
import { kml } from '@tmcw/togeojson';
import { DOMParser } from '@xmldom/xmldom';
import { unByKey } from 'ol/Observable';
import useTrajectoryPlayback from '@/hooks/useTrajectoryPlayback';
import { Modify } from 'ol/interaction';
import ShareLabelModal from './components/ShareLabelModal';
import { sendLocationMessageToUsers } from '@/utils/mapShare';
import { useLocation, useSearchParams } from 'react-router-dom';

// 模拟林班列表数据（用于地图中的点位标记）
// const mockForests = [
//   { id: 1, name: '东北林场1号', area: '2500亩', type: '松树林', position: [116.4074, 39.9142] },
//   { id: 2, name: '南方林场2号', area: '1800亩', type: '杉树林', position: [116.4174, 39.9242] },
//   { id: 3, name: '西部林场3号', area: '3200亩', type: '柏树林', position: [116.4274, 39.9342] },
//   { id: 4, name: '北方林场4号', area: '2100亩', type: '松树林', position: [116.4374, 39.9442] },
//   { id: 5, name: '中部林场5号', area: '2800亩', type: '混合林', position: [116.4474, 39.9542] },
// ];

// 创建地图组件
const MapComponent = ({ setMapInstance, drawnItems }: { setMapInstance: (map: any) => void, drawnItems: any }) => {
  const mapRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // 调试日志
    console.log('MapComponent useEffect执行');
    console.log('mapRef.current存在:', !!mapRef.current);
    
    if (!mapRef.current) {
      console.error('地图容器不存在');
      return;
    }

    // 检查容器尺寸
    console.log('地图容器尺寸:', {
      width: mapRef.current.offsetWidth,
      height: mapRef.current.offsetHeight
    });
    
    // 确保容器有尺寸
    if (mapRef.current.offsetWidth === 0 || mapRef.current.offsetHeight === 0) {
      console.error('地图容器尺寸为0，无法初始化地图');
      // 尝试延迟初始化
      setTimeout(() => {
        if (mapRef.current && mapRef.current.offsetWidth > 0 && mapRef.current.offsetHeight > 0) {
          console.log('延迟后容器尺寸:', {
            width: mapRef.current.offsetWidth,
            height: mapRef.current.offsetHeight
          });
          initMap();
        }
      }, 100);
      return;
    }
    
    initMap();
    
    function initMap() {
      if (!mapRef.current) return;
      
      // 创建OSM基础图层
      const osmLayer = new TileLayer({
        source: new OSM(),
        visible: false,
        properties: {
          name: '标准地图'
        }
      });

      // 创建卫星图层 - 使用 ArcGIS 世界影像图层
      const satelliteLayer = new TileLayer({
        source: new XYZ({
          url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
          maxZoom: 15
        }),
        visible: true,
        properties: {
          name: '卫星影像'
        }
      });

      // 创建混合卫星图层（带标签）
      const hybridLayer = new TileLayer({
        source: new XYZ({
          url: 'https://mt1.google.com/vt/lyrs=y&x={x}&y={y}&z={z}',
          maxZoom: 20
        }),
        visible: false,
        properties: {
          name: '混合影像'
        }
      });

      // 创建地形图层
      const terrainLayer = new TileLayer({
        source: new XYZ({
          url: 'https://mt1.google.com/vt/lyrs=p&x={x}&y={y}&z={z}',
          maxZoom: 20
        }),
        visible: false,
        properties: {
          name: '地形地图'
        }
      });
      
      // 用户位置矢量图层
      const usersSource = new VectorSource();
      const usersLayer = new VectorLayer({
        source: usersSource,
        style: function(feature) {
          const user = feature.get('user');
          return new Style({
            image: new CircleStyle({
              radius: 6,
              fill: new Fill({
                color: user.online ? '#52c41a' : '#d9d9d9'
              }),
              stroke: new Stroke({
                color: 'white',
                width: 2
              })
            }),
            text: new Text({
              text: user.name,
              offsetY: -15,
              fill: new Fill({
                color: '#333'
              }),
              stroke: new Stroke({
                color: 'white',
                width: 3
              })
            })
          });
        },
        properties: {
          name: '人员位置'
        }
      });

      // 林班位置图层
      const forestsSource = new VectorSource();
      const forestsLayer = new VectorLayer({
        source: forestsSource,
        style: function(feature) {
          const forest = feature.get('forest');
          return new Style({
            image: new CircleStyle({
              radius: 6,
              fill: new Fill({
                color: '#87d068'
              }),
              stroke: new Stroke({
                color: 'white',
                width: 2
              })
            }),
            text: new Text({
              text: forest.name,
              offsetY: -15,
              fill: new Fill({
                color: '#333'
              }),
              stroke: new Stroke({
                color: 'white',
                width: 3
              })
            })
          });
        },
        properties: {
          name: '林班位置'
        }
      });

      // 绘制图层
      const drawSource = new VectorSource();
      const drawLayer = new VectorLayer({
        source: drawSource,
        style: function(feature) {
          return new Style({
            fill: new Fill({
              color: feature.get('fillColor') || 'rgba(255, 255, 255, 0.2)'
            }),
            stroke: new Stroke({
              color: '#ffcc33',
              width: 2
            }),
            image: new CircleStyle({
              radius: 7,
              fill: new Fill({
                color: '#ffcc33'
              })
            })
          });
        },
        properties: {
          name: '绘制图层'
        }
      });

      try {
        // 创建地图
        const map = new OlMap({
          target: mapRef.current,
          layers: [
            osmLayer,
            satelliteLayer,
            hybridLayer,
            terrainLayer,
            usersLayer,
            forestsLayer,
            drawLayer
          ],
          view: new View({
            center: fromLonLat([116.4074, 39.9042]), // 北京坐标
            zoom: 13,
            maxZoom: 20
          })
        });

        console.log('地图实例已创建:', !!map);

        // 添加林班标记
        // mockForests.forEach(forest => {
        //   const feature = new Feature({
        //     geometry: new Point(fromLonLat(forest.position)),
        //     forest: forest
        //   });
        //   forestsSource.addFeature(feature);
        // });

        // 提供map实例给父组件
        setMapInstance(map);
        
        // 更新地图大小，确保正确渲染
        setTimeout(() => {
          map.updateSize();
          console.log('地图大小已更新');
        }, 200);

        const style = document.createElement('style');
        style.innerHTML = `
          .map-coordinate-display {
            position: absolute;
            left: 10px;
            bottom: 50px;
            background: rgba(255, 255, 255, 0.1);
            padding: 8px 12px;
            border-radius: 4px;
            font-family: Arial, sans-serif;
            font-size: 14px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            display: inline-block;
            white-space: nowrap;
            color: white;
            transition: left 0.3s ease;
          }

          .map-coordinate-display.sidebar-collapsed {
            left: 10px;
          }
        `;
        document.head.appendChild(style);

    return () => {
          document.head.removeChild(style);
      console.log('地图组件卸载，清理地图实例');
      setMapInstance(null);
    };
      } catch (error) {
        console.error('地图初始化错误:', error);
      }
    }
  }, []);

  return (
    <div 
      ref={mapRef} 
                      style={{
        height: '100%', 
        width: '100%',
                        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: 'rgba(200, 200, 200, 0.1)'
      }}
    ></div>
  );
};

const CoordinateDisplay = ({ map }: { map: OlMap | null }) => {
  const [coordinate, setCoordinate] = useState({ lon: 0, lat: 0 });
  const [zoomLevel, setZoomLevel] = useState(0);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  useEffect(() => {
    if (!map) return;

    const handlePointerMove = (evt: any) => {
      try {
        const coords = map.getCoordinateFromPixel(evt.pixel);
        if (coords) {
          const lonLat = toLonLat(coords);
          setCoordinate({ lon: lonLat[0], lat: lonLat[1] });
        }
      } catch (e) {
        // Can happen if projection is not ready
        console.error(e);
      }
    };

    const handleZoomChange = () => {
      setZoomLevel(map.getView().getZoom() || 0);
    };

    const pointerMoveKey = map.on('pointermove', handlePointerMove);
    const view = map.getView();
    const zoomChangeKey = view.on('change:resolution', handleZoomChange);
    
    setZoomLevel(view.getZoom() || 0);

    return () => {
      unByKey(pointerMoveKey);
      unByKey(zoomChangeKey);
    };
  }, [map]);

  return (
    <div className={`map-coordinate-display ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`}>
      经度: {coordinate.lon.toFixed(6)} |
      纬度: {coordinate.lat.toFixed(6)} |
      级别: {zoomLevel.toFixed(2)}
    </div>
  );
};

// 添加版权信息组件
const CopyrightInfo = () => {
  return (
    <div style={{
      position: 'fixed', // 改为fixed定位，相对于视口
      bottom: '5px',
      right: '5px',
      backgroundColor: 'rgba(255, 255, 255, 0.8)',
      padding: '4px 8px',
      fontSize: '12px',
      color: '#666',
      zIndex: 9000,  
      borderTopLeftRadius: '4px',
      pointerEvents: 'none',  // 不阻止鼠标事件
      boxShadow: '0 1px 4px rgba(0,0,0,0.1)' // 添加轻微阴影提高可见度
    }}>
      © 2025 林班管理系统 版权所有
    </div>
  );
};

// 主页组件
const Home = () => {
  // 使用状态来跟踪客户端渲染
  const [isClient, setIsClient] = useState(false);
  const [mapInstance, setMapInstance] = useState<OlMap | null>(null);
  const [drawingInteraction, setDrawingInteraction] = useState<Draw | null>(null);
  const [selectedFeature, setSelectedFeature] = useState<any>(null);
  const [selectedUserInfo, setSelectedUserInfo] = useState<{ user: any; coordinate: { lon: number; lat: number } } | null>(null);
  const playback = useTrajectoryPlayback(mapInstance);
  const [trajectoryLayer, setTrajectoryLayer] = useState<VectorLayer<VectorSource> | null>(null);
  const [animLayer, setAnimLayer] = useState<VectorLayer<VectorSource> | null>(null);
  const [selectedUserLayer, setSelectedUserLayer] = useState<VectorLayer<VectorSource> | null>(null);
  const animationFrameId = useRef<number | null>(null);
  const [drawnItems, setDrawnItems] = useState<any[]>([]);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [drawType, setDrawType] = useState<string | null>(null);
  const [isPanelVisible, setIsPanelVisible] = useState(true);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [userTrajectory, setUserTrajectory] = useState<any>(null);
  const [highlightedUserFeature, setHighlightedUserFeature] = useState<Feature | null>(null);
  const trajectoryLayerRef = useRef<VectorLayer<any> | null>(null);
  const [drawingFeature, setDrawingFeature] = useState<Feature | null>(null);
  const modifyInteractionRef = useRef<Modify | null>(null);
  // 新增标注图层引用
  const labelsLayerRef = useRef<VectorLayer<any> | null>(null);
  // 新增当前选中的林班ID
  const [currentLinbanId, setCurrentLinbanId] = useState<number | null>(null);
  // 新增林班边界图层引用
  const linbanBoundaryLayerRef = useRef<VectorLayer<any> | null>(null);
  
  const mapRef = useRef<OlMap | null>(null);
  const userId = useUserStore((state) => state.linBanUser.id);
  const { mutate: addFavoriteMutate, isLoading: isAddingFavorite } = useAddFavorite();
  const { mutate: createLabelMutate, isLoading: isCreatingLabel } = useCreateLabel();
  // 使用获取所有标注点的API钩子
  const { data: labelsData } = useGetAllLabels(String(userId));
  const queryClient = useQueryClient();
  const [selectedLabelId, setSelectedLabelId] = useState<string | null>(null);
  const { data: labelDetailData, isLoading: isLabelDetailLoading } = useGetLabelById(selectedLabelId || '', {
    enabled: !!selectedLabelId
  });
  const { mutate: updateLabelMutate, isLoading: isUpdatingLabel } = useUpdateLabel();
  const { mutate: deleteLabelMutate, isLoading: isDeletingLabel } = useDeleteLabel();
  const [isEditMode, setIsEditMode] = useState(false);
  const [shareModalVisible, setShareModalVisible] = useState(false);
  const [shareLoading, setShareLoading] = useState(false);
  const [selectedLabelForShare, setSelectedLabelForShare] = useState<any>(null);
  const [searchParams] = useSearchParams();
  
  useEffect(() => {
    setIsClient(true);
    console.log('Home组件已渲染，isClient设置为true');
    
    // 创建样式标签
    const styleEl = document.createElement('style');
    styleEl.innerHTML = `
      html, body, #root {
        height: 100%;
        width: 100%;
        margin: 0;
        padding: 0;
        overflow: hidden;
      }
    `;
    document.head.appendChild(styleEl);
    
    if (mapInstance) {
      // 通知地图初始化完成，提供地图实例
      emitter.emit("MAP_INITIALIZED", mapInstance);
      
      // 设置点击选择功能
      mapInstance.on('click', function(evt) {
        let featureHandled = false;
        mapInstance.forEachFeatureAtPixel(evt.pixel, function(feature, layer) {
          if (featureHandled) return; // 如果已经处理过，就跳过
          
          const user = feature.get('user');
          if (user) {
            setSelectedUserInfo({ user, coordinate: feature.get('coordinate') });
            featureHandled = true;
            return;
          }
          
          // 检查是否点击了标注
          const labelId = feature.get('labelId');
          if (labelId) {
            console.log('点击了标注，ID:', labelId);
            setSelectedLabelId(labelId);
            setSelectedFeature(feature);
            featureHandled = true;
            return;
          }
        });

        if (!featureHandled) {
          const genericFeature = mapInstance.forEachFeatureAtPixel(evt.pixel, function(f) { return f; });
          if (genericFeature) {
            setSelectedFeature(genericFeature);
          } else {
            setSelectedFeature(null);
            setSelectedLabelId(null);
          }
        }
      });

      // 添加绘图交互
      const drawVectorLayer = mapInstance.getLayers().getArray().find(layer => 
        layer.get('name') === '绘制图层'
      ) as VectorLayer<VectorSource>;

      if (drawVectorLayer) {
        // 移除自动添加绘制交互的代码，让用户通过工具栏来启动绘制
        // const drawInteraction = new Draw({
        //   source: drawVectorLayer.getSource() as VectorSource,
        //   type: 'Polygon'
        // });

        // drawInteraction.on('drawend', function(evt) {
        //   evt.feature.set('name', '临时绘制');
        //   setDrawingFeature(evt.feature); // Show the new info panel
        //   setDrawingInteraction(null); // Deactivate drawing
        //   message.info('绘制完成，请填写信息并保存。');
        // });

        // setDrawingInteraction(drawInteraction);
        // mapInstance.addInteraction(drawInteraction);
      }
    }

    // 添加全局样式确保地图容器有高度
    const style = document.createElement('style');
    style.innerHTML = `
      html, body, #root {
        height: 100%;
        margin: 0;
        padding: 0;
      }
      
      .tab-content-panel {
        /* padding: 12px; */
        height: 100%;
        display: flex;
        flex-direction: column;
      }
      
      .tab-header {
        margin-bottom: 12px;
      }
      
      .tab-body {
        flex: 1;
        overflow-y: auto;
      }
      
      .active-item {
        background-color: #e6f7ff;
      }
      
      .ant-list-item {
        padding: 6px 8px;
        cursor: pointer;
        transition: background-color 0.3s;
        border-radius: 4px;
        margin-bottom: 4px;
        display: flex;
        align-items: center;
      }
      
      .sidebar-tabs .ant-list-item-meta {
        align-items: center;
        display: flex;
      }
      
      .sidebar-tabs .ant-list-item-meta-avatar {
        display: flex;
        align-items: center;
        margin-right: 12px;
      }
      
      .sidebar-tabs .ant-list-item-meta-content {
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      
      .sidebar-tabs .ant-list-item-meta-title {
        font-size: 13px;
        margin-bottom: 0;
        line-height: 1.5;
      }
      
      .sidebar-tabs .ant-list-item-meta-description {
        font-size: 12px;
        line-height: 1.2;
      }
      
      .ant-list-item:hover {
        background-color: #f5f5f5;
      }

      .map-layer-switcher {
        position: absolute;
        top: 10px;
        right: 10px;
        background: white;
        padding: 5px;
        border-radius: 4px;
        box-shadow: 0 2px 6px rgba(0,0,0,0.1);
        z-index: 1000;
      }

      .map-layer-switcher .layer-options {
        padding: 5px;
        margin-top: 5px;
        border-top: 1px solid #f0f0f0;
      }

      .side-panel .ant-tabs-nav {
        margin-bottom: 0;
      }

      .ol-tooltip {
        position: relative;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 4px;
        color: white;
        padding: 4px 8px;
        opacity: 0.7;
        white-space: nowrap;
        font-size: 12px;
      }
      .ol-tooltip-measure {
        opacity: 1;
        font-weight: bold;
      }
      .ol-tooltip-static {
        background-color: #ffcc33;
        color: black;
        border: 1px solid white;
      }
      .ol-tooltip-measure:before,
      .ol-tooltip-static:before {
        border-top: 6px solid rgba(0, 0, 0, 0.5);
        border-right: 6px solid transparent;
        border-left: 6px solid transparent;
        content: "";
        position: absolute;
        bottom: -6px;
        margin-left: -7px;
        left: 50%;
      }
      .ol-tooltip-static:before {
        border-top-color: #ffcc33;
      }
    `;
    document.head.appendChild(style);
    
    // 监听绘制完成事件
    const handleDrawComplete = (params: DrawCompleteParams) => {
      console.log('接收到绘制完成事件:', params);
      if (params.feature) {
        // 设置临时名称
        params.feature.set('name', '临时绘制');
        // 显示绘制信息面板
        setDrawingFeature(params.feature);
        
        // 添加修改交互，允许立即拖动编辑几何形状
        if (mapInstance) {
          // 如果存在之前的修改交互，先移除
          if (modifyInteractionRef.current) {
            mapInstance.removeInteraction(modifyInteractionRef.current);
            modifyInteractionRef.current = null;
          }
          
          // 创建新的修改交互
          const modifyInteraction = new Modify({
            features: new Collection([params.feature]),
          });
          
          mapInstance.addInteraction(modifyInteraction);
          modifyInteractionRef.current = modifyInteraction;
          console.log('已添加修改交互，可拖动锚点编辑');
        }
        
        console.log('绘制完成，已设置绘制特征，显示信息面板');
      }
    };

    // 监听林班筛选事件
    const handleFilterByLinban = (params: FilterByLinbanParams) => {
      console.log('接收到林班筛选事件:', params);
      setCurrentLinbanId(params.linbanId);
      
      // 如果有林班ID，筛选标注和显示林班边界
      if (params.linbanId !== null) {
        // 筛选标注
        filterLabelsByLinban(params.linbanId);
        
        // 显示林班边界
        showLinbanBoundary(params.linbanId);
      } else {
        // 如果清除了选择，显示所有标注
        if (labelsData?.data) {
          displayAllLabels(labelsData.data);
        }
        
        // 清除林班边界
        clearLinbanBoundary();
      }
    };

    // 清理原有的绘制交互（如果有）
    if (drawingInteraction && mapInstance) {
      mapInstance.removeInteraction(drawingInteraction);
      setDrawingInteraction(null);
    }

    emitter.on("DRAW_COMPLETE", handleDrawComplete);
    emitter.on("FILTER_BY_LINBAN", handleFilterByLinban);
    
    return () => {
      document.head.removeChild(style);
      document.head.removeChild(styleEl);
      if (drawingInteraction) {
        mapInstance?.removeInteraction(drawingInteraction);
      }
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }
      // 移除事件监听
      emitter.off("DRAW_COMPLETE", handleDrawComplete);
      emitter.off("FILTER_BY_LINBAN", handleFilterByLinban);
    };
  }, [mapInstance]);

  // Effect to handle modify interaction based on selected feature
  useEffect(() => {
    // If a modify interaction exists from a previous selection, remove it.
    if (modifyInteractionRef.current) {
        mapInstance?.removeInteraction(modifyInteractionRef.current);
        modifyInteractionRef.current = null;
    }

    // If we have a map and a selected feature (that is an OL feature), create a new modify interaction
    if (mapInstance && selectedFeature && typeof selectedFeature.get === 'function') {
        const geometry = selectedFeature.getGeometry();
        // We only want to modify polygons and lines, not points for now.
        if (geometry && (geometry.getType() === 'Polygon' || geometry.getType() === 'LineString')) {

            const newModifyInteraction = new Modify({
                features: new Collection([selectedFeature]),
            });

            mapInstance.addInteraction(newModifyInteraction);

            // Store the new interaction in the ref
            modifyInteractionRef.current = newModifyInteraction;
        }
    }
  }, [selectedFeature, mapInstance]);

  // 监听绘制特征变化，当特征被清除时移除修改交互
  useEffect(() => {
    if (!drawingFeature && mapInstance && modifyInteractionRef.current) {
      // 绘制特征被清除，移除修改交互
      mapInstance.removeInteraction(modifyInteractionRef.current);
      modifyInteractionRef.current = null;
    }
  }, [drawingFeature, mapInstance]);

  const handlePropertyChange = (feature: any, newProperties: any) => {
    if (!feature) return;

    // Helper to convert HEX and opacity to an RGBA color string
    const hexToRgba = (hex: string, alpha: number) => {
      // Default to a blueish color if hex is invalid
      if (!hex || !/^#([A-Fa-f0-9]{6})$/.test(hex)) {
        return `rgba(51, 136, 255, ${alpha})`;
      }
      const r = parseInt(hex.slice(1, 3), 16);
      const g = parseInt(hex.slice(3, 5), 16);
      const b = parseInt(hex.slice(5, 7), 16);
      return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    };

    // 创建样式
    const style = new Style({
      fill: new Fill({
        color: hexToRgba(newProperties.fillColor, newProperties.opacity),
      }),
      stroke: new Stroke({
        color: hexToRgba(newProperties.strokeColor, newProperties.strokeOpacity),
        width: newProperties.strokeWeight,
        lineDash: newProperties.strokeStyle === 'dashed' ? [10, 10] : undefined,
      }),
    });
    
    feature.setStyle(style);
    feature.set('name', newProperties.name);
    feature.set('customInfo', newProperties);
    
    // 如果是标注，并且有标注ID，可以在这里添加更新标注的API调用
    const labelId = feature.get('labelId');
    if (labelId) {
      // TODO: 调用更新标注API
      console.log('更新标注属性:', labelId, newProperties);
    }
  };

  const toggleDrawing = () => {
    if (!mapInstance) return;

    if (drawingInteraction) {
      // If interaction exists, we are turning it OFF
      mapInstance.removeInteraction(drawingInteraction);
      setDrawingInteraction(null);
    } else {
      // If it doesn't exist, we are turning it ON
      const drawSource = (mapInstance.getLayers().getArray().find(l => l.get('name') === '绘制图层') as VectorLayer<any>)?.getSource();
      if (!drawSource) return;

      const newDrawInteraction = new Draw({
        source: drawSource,
        type: 'Polygon',
      });

      newDrawInteraction.on('drawend', function(evt) {
        evt.feature.set('name', '临时绘制');
        setDrawingFeature(evt.feature);
        // Deactivate drawing temporarily, but don't remove the interaction object yet
        newDrawInteraction.setActive(false);
      });

      mapInstance.addInteraction(newDrawInteraction);
      setDrawingInteraction(newDrawInteraction);
    }
  };

  // 图层控制器组件
  const LayerSwitcher = ({ map }: { map: OlMap | null }) => {
    const [layers, setLayers] = useState<Array<{name: string, visible: boolean}>>([]);
    const [expanded, setExpanded] = useState(false);
    
    useEffect(() => {
      if (!map) return;
      
      const layerArray = map.getLayers().getArray();
      const baseLayers = layerArray.filter(layer => 
        ['标准地图', '卫星影像', '混合影像', '地形地图'].includes(layer.get('name') as string)
      ).map(layer => ({
        name: layer.get('name') as string,
        visible: layer.getVisible()
      }));
      
      setLayers(baseLayers);
    }, [map]);
    
    const handleLayerChange = (layerName: string) => {
      if (!map) return;
      
      const layerArray = map.getLayers().getArray();
      
      // 更新基础图层可见性
      layerArray.forEach(layer => {
        if (['标准地图', '卫星影像', '混合影像', '地形地图'].includes(layer.get('name') as string)) {
          layer.setVisible(layer.get('name') === layerName);
        }
      });
      
      // 更新状态
      setLayers(prev => prev.map(l => ({
        ...l,
        visible: l.name === layerName
      })));
    };

    const getSelectedLayerName = () => {
      const selectedLayer = layers.find(l => l.visible);
      return selectedLayer ? selectedLayer.name : "选择地图";
    }
  
  return (
      <div className="map-layer-switcher">
        <Button onClick={() => setExpanded(!expanded)}>
          {getSelectedLayerName()} {expanded ? '▲' : '▼'}
        </Button>
        {expanded && (
          <div className="layer-options">
        {layers.map((layer) => (
          <div key={layer.name}>
            <label>
              <input
                type="radio"
                name="baseLayer"
                checked={layer.visible}
                onChange={() => handleLayerChange(layer.name)}
              />
              {' '}{layer.name}
            </label>
          </div>
        ))}
          </div>
        )}
      </div>
    );
  };
  
  // 点击人员列表后定位并弹窗
  const handleUserSelect = (user: any, coordinate: { lon: number; lat: number }) => {
    playback.clearTrajectory();
    setSelectedUserInfo({ user, coordinate });

    if (mapInstance) {
      mapInstance.getView().animate({ center: fromLonLat([coordinate.lon, coordinate.lat]), zoom: 16, duration: 500 });
      
      // 创建或获取高亮图层
      let highLightLayer = selectedUserLayer;
      if (!highLightLayer) {
        const source = new VectorSource();
        highLightLayer = new VectorLayer({
          source,
          zIndex: 999, // 确保图层在最上层
          properties: { name: 'selected-user' },
          style: new Style({
            image: new CircleStyle({
              radius: 10,
              fill: new Fill({ color: 'rgba(255, 0, 0, 0.6)' }),
              stroke: new Stroke({ color: 'white', width: 3 }),
            }),
          }),
        });
        mapInstance.addLayer(highLightLayer);
        setSelectedUserLayer(highLightLayer);
      }
      
      // 清除旧标记并添加新标记
      const source = highLightLayer.getSource();
      if (source) {
        source.clear();
        const feature = new Feature({
          geometry: new Point(fromLonLat([coordinate.lon, coordinate.lat])),
          user: user,
          coordinate: coordinate,
        });
        source.addFeature(feature);
      }
    }
  };

  // 查看24小时轨迹
  const handleViewTrajectory = async (userId: number) => {
    if (!mapInstance) return;

    try {
      const res = await getTrajectory(String(userId));
      const pts = res.data;

      if (!Array.isArray(pts) || pts.length === 0) {
        message.info('该用户在过去24小时内没有轨迹数据');
        return;
      }

      const coords = pts.map((p: any) => fromLonLat([p.longitude, p.latitude]));

      // 清除旧图层
      if (trajectoryLayer && mapInstance) mapInstance.removeLayer(trajectoryLayer);
      if (animLayer && mapInstance) mapInstance.removeLayer(animLayer);

      // 绘制线路
      const lineFeat = new Feature(new LineString(coords));
      const trajSource = new VectorSource({ features: [lineFeat] });
      const trajLayer = new VectorLayer({
        source: trajSource,
        style: new Style({ stroke: new Stroke({ color: '#ff0000', width: 3 }) }),
        properties: { name: 'trajectory' }
      });
      mapInstance.addLayer(trajLayer);
      setTrajectoryLayer(trajLayer);

      // 动画点
      const ptFeat = new Feature(new Point(coords[0]));
      ptFeat.setStyle(new Style({ image: new CircleStyle({ radius: 8, fill: new Fill({ color: 'blue' }), stroke: new Stroke({ color: 'white', width: 2 }) }) }));
      const animSource = new VectorSource({ features: [ptFeat] });
      const animLayerTmp = new VectorLayer({ source: animSource, properties: { name: 'trajectory-animation' } });
      mapInstance.addLayer(animLayerTmp);
      setAnimLayer(animLayerTmp);

      // 基于 requestAnimationFrame 的新动画逻辑
      const duration = 10000; // 动画总时长 (毫秒)
      let startTime: number | null = null;

      const animate = (timestamp: number) => {
        if (!startTime) {
          startTime = timestamp;
        }

        const elapsedTime = timestamp - startTime;
        const progress = Math.min(elapsedTime / duration, 1);
        
        const index = Math.floor(progress * (coords.length - 1));
        const currentPos = coords[index];

        const geom = ptFeat.getGeometry();
        if (geom) {
          (geom as Point).setCoordinates(currentPos);
        }

        if (progress < 1) {
          animationFrameId.current = requestAnimationFrame(animate);
        }
      };

      animationFrameId.current = requestAnimationFrame(animate);

    } catch (err) {
      console.error('获取轨迹失败', err);
      message.error('获取轨迹数据失败');
    }
  };

  const handleFavoriteSelect = (feature: any) => {
    if (highlightedUserFeature) {
      const source = (mapInstance?.getLayers().getArray().find(l => l.get('name') === 'highlight-layer') as VectorLayer<any>)?.getSource();
      if (source) {
        source.removeFeature(highlightedUserFeature);
      }
      setHighlightedUserFeature(null);
    }

    if (trajectoryLayerRef.current) {
      mapInstance?.removeLayer(trajectoryLayerRef.current);
      trajectoryLayerRef.current = null;
    }

    setSelectedUser(null);
    setSelectedFeature(feature);
  };
  
  useEffect(() => {
    // This effect should ONLY run for GeoJSON objects from the favorites panel,
    // not for ol/Feature objects from drawing or map clicks.
    if (mapInstance && selectedFeature && typeof selectedFeature.get !== 'function') {
      // 移除上一个收藏图层
      mapInstance.getLayers().getArray()
        .filter(layer => layer.get('name') === 'favorite-layer')
        .forEach(layer => mapInstance.removeLayer(layer));

      const vectorSource = new VectorSource({
        features: new GeoJSON().readFeatures(selectedFeature, {
          featureProjection: 'EPSG:3857' // 确保坐标转换到地图的投影
        })
      });

      const favoriteLayer = new VectorLayer({
        source: vectorSource,
        style: function(feature) {
          const properties = feature.getProperties();
          const color = properties.color || '#ff4d4f'; // 默认红色
          const lineWidth = properties.lineWidth || 3;

          // 将HEX颜色转为带透明度的RGBA
          const hexToRgba = (hex: string, alpha: number) => {
            if (!/^#([A-Fa-f0-9]{6})$/.test(hex)) {
              return `rgba(255, 77, 79, ${alpha})`; // 无效hex则返回默认颜色
            }
            const r = parseInt(hex.slice(1, 3), 16);
            const g = parseInt(hex.slice(3, 5), 16);
            const b = parseInt(hex.slice(5, 7), 16);
            return `rgba(${r}, ${g}, ${b}, ${alpha})`;
          };

          return new Style({
            stroke: new Stroke({
              color: color,
              width: lineWidth
            }),
            fill: new Fill({
              color: hexToRgba(color, 0.3) // 填充颜色使用30%透明度
            }),
            image: new CircleStyle({
              radius: 7,
              fill: new Fill({
                color: color
              }),
              stroke: new Stroke({
                color: 'white',
                width: 2
              })
            })
          });
        },
        properties: {
          name: 'favorite-layer'
        }
      });

      mapInstance.addLayer(favoriteLayer);

      // 缩放到 feature
      if (vectorSource.getFeatures().length > 0) {
        mapInstance.getView().fit(vectorSource.getExtent(), {
          padding: [100, 100, 100, 100],
          duration: 1000,
          maxZoom: 17
        });
      }
    }
  }, [mapInstance, selectedFeature]);

  // 切换侧边栏可见性
  const togglePanel = () => {
    setIsPanelVisible(!isPanelVisible);
  };

  const handleDrawSelect = (type: string) => {
    if (!mapInstance) return;
    
    console.log('Draw type selected:', type);
    
    // 触发显示地图工具栏的事件
    if (type === 'polygon') {
      emitter.emit("SHOW_MAP_TOOLBAR", { type: "polygon", isAnnotation: true });
    } else if (type === 'linestring') {
      emitter.emit("SHOW_MAP_TOOLBAR", { type: "linestring", isAnnotation: true });
    } else if (type === 'point') {
      emitter.emit("SHOW_MAP_TOOLBAR", { type: "marker", isAnnotation: true });
    }
  };

  const handlePlotSelect = (plot: any) => {
    // TODO: 实现林班选择逻辑
    console.log('Plot selected:', plot);
  };

  const handleCancelDrawing = () => {
    // 清理绘制图层中的所有特征，包括临时坐标点
    const drawLayer = mapInstance?.getLayers().getArray().find(l => l.get('name') === '绘制图层') as VectorLayer<any>;
    if (drawLayer) {
      const drawSource = drawLayer.getSource();
      if (drawSource) {
        // 清空整个图层的源数据，移除所有特征
        drawSource.clear();
      }
    }
    
    // 重置绘制状态
    setDrawingFeature(null);
    
    // 如果绘制交互存在，则移除它
    if (mapInstance) {
      if (drawingInteraction) {
        mapInstance.removeInteraction(drawingInteraction);
        setDrawingInteraction(null);
      }
      
      // 移除修改交互
      if (modifyInteractionRef.current) {
        mapInstance.removeInteraction(modifyInteractionRef.current);
        modifyInteractionRef.current = null;
      }
      
      message.info('已取消绘制');
    }
  };

  const handleSaveDrawing = (values: { name: string; description?: string; quartelId?: number }) => {
    if (!drawingFeature) return;

    const geometry = drawingFeature.getGeometry();
    if (!geometry) return;

    // 获取几何类型
    const geometryType = geometry.getType(); // 'Point', 'LineString', 'Polygon'等

    const geoJsonWriter = new GeoJSON();
    const geometryJson = geoJsonWriter.writeGeometryObject(geometry, {
      featureProjection: 'EPSG:3857',
      dataProjection: 'EPSG:4326',
    });

    const featureForSave = {
      type: 'Feature',
      geometry: geometryJson,
      properties: {
        title: values.name,
        description: values.description || '',
        color: "#4285F4", // 默认颜色
        lineWidth: 3,    // 默认线宽
      }
    };
    
    // 准备API调用参数
    const createLabelParams: {
      labelName: string;
      labelRemark: string;
      dataJson: any; // 修改为对象类型
      type: string;
      userId: string;
      quartelId?: number;
    } = {
      labelName: values.name,
      labelRemark: values.description || '',
      dataJson: featureForSave, // 直接使用对象，不用JSON.stringify
      type: geometryType, // 使用实际的几何类型
      userId: String(userId),
    };
    
    // 如果有林班ID，添加到参数
    if (values.quartelId) {
      createLabelParams.quartelId = values.quartelId;
    }
    
    console.log('创建标注请求参数:', createLabelParams);
    
    // 创建标记点
    createLabelMutate(createLabelParams, {
      onSuccess: (response) => {
        // 从响应中获取新创建的标记点ID
        const labelId = response.data?.id;
        if (!labelId) {
          message.error('创建标记点失败，未返回ID');
          return;
        }
        
        message.success('标注创建成功！');
            
            // 移除临时绘制图层中的特征
            const drawSource = (mapInstance?.getLayers().getArray().find(l => l.get('name') === '绘制图层') as VectorLayer<any>)?.getSource();
            if (drawSource && drawingFeature) {
              drawSource.removeFeature(drawingFeature);
            }
            setDrawingFeature(null);
        
        // 移除修改交互
        if (mapInstance && modifyInteractionRef.current) {
          mapInstance.removeInteraction(modifyInteractionRef.current);
          modifyInteractionRef.current = null;
        }
        
        // 刷新标注列表
        queryClient.invalidateQueries('getAllLabels');
            queryClient.invalidateQueries('getFavorites');
          },
      onError: (error) => {
        console.error('创建标注失败:', error);
        message.error('创建标注失败，请重试');
      }
    });
  };

  const addWMSLayers = (wmsData: { url: string, layers: Array<{ name: string }> }) => {
    if (!mapInstance) return [];
    
    let wmsLayers = [];
    for (let i = 0; i < wmsData.layers.length; i++) {
      const wmsLayer = new TileLayer({
        source: new TileWMS({
          url: wmsData.url,
          params: {
            'LAYERS': wmsData.layers[i].name,
            'TILED': true,
            'TRANSPARENT': true
          },
          serverType: 'geoserver'
        }),
        properties: {
          name: wmsData.layers[i].name
        }
      });
      
      // 只显示第一个图层
      i === 0 ? wmsLayer.setVisible(true) : wmsLayer.setVisible(false);
      
      wmsLayers.push(wmsLayer);
      mapInstance.addLayer(wmsLayer);
    }
    
    return wmsLayers;
  };

  const addGeoJsonToMap = (geojsonData: any) => {
    if (!mapInstance) return;
    
    const drawLayer = mapInstance.getLayers().getArray().find(layer => 
      layer.get('name') === '绘制图层'
    ) as VectorLayer<VectorSource>;
    
    if (!drawLayer) return;
    
    const source = drawLayer.getSource();
    if (!source) return;
    
    const features = new GeoJSON().readFeatures(geojsonData, {
      featureProjection: 'EPSG:3857'
    });
    
    source.addFeatures(features);
    
    // 设置默认样式
    features.forEach(feature => {
      const defaultStyle = {
        strokeColor: '#ff0000',
        strokeWeight: 3,
        strokeOpacity: 0.7,
        strokeStyle: 'dashed',
        fillColor: '#ffff00',
        fillOpacity: 0.5,
        label: '导入数据'
      };
      
      feature.set('customInfo', defaultStyle);
    });
    
    // 缩放到数据范围
    if (features.length > 0) {
      mapInstance.getView().fit(source.getExtent(), {
        padding: [50, 50, 50, 50],
        duration: 2000,
        maxZoom: 19
      });
    }
  };

  const addOVKMLToMap = (ovkmlString: string) => {
    try {
      // 解析KML字符串为XML文档
      const xmlDoc = new DOMParser().parseFromString(ovkmlString, 'text/xml');
      
      // 转换为GeoJSON
      const geojsonData = kml(xmlDoc);
      
      // 添加到地图
      addGeoJsonToMap(geojsonData);
    } catch (error) {
      console.error('解析OVKML失败:', error);
      message.error('解析OVKML失败');
    }
  };

  // 添加显示所有标注点的函数
  const displayAllLabels = (labels: any[]) => {
    if (!mapInstance) return;
    
    // 创建标注图层（如果不存在）
    if (!labelsLayerRef.current) {
      const labelsSource = new VectorSource();
      const labelsLayer = new VectorLayer({
        source: labelsSource,
        style: function(feature) {
          const customStyle = feature.get('customStyle') || {};
          return new Style({
            fill: new Fill({
              color: customStyle.fillColor || 'rgba(255, 255, 255, 0.2)'
            }),
            stroke: new Stroke({
              color: customStyle.strokeColor || '#ffcc33',
              width: customStyle.strokeWidth || 2
            }),
            image: new CircleStyle({
              radius: customStyle.radius || 7,
              fill: new Fill({
                color: customStyle.pointColor || '#ffcc33'
              })
            }),
            text: customStyle.text ? new Text({
              text: customStyle.text,
              offsetY: -15,
              fill: new Fill({
                color: '#333'
              }),
              stroke: new Stroke({
                color: 'white',
                width: 3
              })
            }) : undefined
          });
        },
        properties: {
          name: '标注图层'
        }
      });
      
      labelsLayerRef.current = labelsLayer;
      mapInstance.addLayer(labelsLayer);
    }
    
    // 清除现有标注
    const labelsSource = labelsLayerRef.current.getSource();
    labelsSource.clear();
    
    // 添加新标注
    labels.forEach(label => {
      try {
        // 解析GeoJSON数据
        if (label.dataJson) {
          let geojsonData;
          if (typeof label.dataJson === 'string') {
            // 如果是字符串，尝试解析
            geojsonData = JSON.parse(label.dataJson);
          } else {
            // 如果已经是对象，直接使用
            geojsonData = label.dataJson;
          }
          
          const features = new GeoJSON().readFeatures(geojsonData, {
            featureProjection: 'EPSG:3857'
          });
          
          // 设置自定义属性
          features.forEach(feature => {
            // 从GeoJSON提取属性或使用默认值
            const properties = geojsonData.properties || {};
            
            // 获取颜色、线宽和标题（如果存在）
            const customStyle = {
              text: label.labelName || properties.title || '',
              fillColor: properties.color ? `${properties.color}33` : 'rgba(66, 133, 244, 0.2)', // 添加透明度
              strokeColor: properties.color || '#4285F4',
              strokeWidth: properties.lineWidth || 2,
              pointColor: properties.color || '#4285F4'
            };
            
            feature.set('customStyle', customStyle);
            feature.set('labelId', label.id);
            feature.set('labelName', label.labelName || properties.title || '');
            feature.set('labelRemark', label.labelRemark || properties.description || '');
            feature.set('properties', properties);
          });
          
          // 添加到图层
          labelsSource.addFeatures(features);
        }
      } catch (error) {
        console.error('解析标注数据失败:', error);
      }
    });
  };

  // 使用useEffect监听标注数据变化
  useEffect(() => {
    if (labelsData?.data) {
      console.log('获取到标注数据:', labelsData.data);
      displayAllLabels(labelsData.data);
    }
  }, [labelsData, mapInstance]);

  // 处理标注详情数据
  useEffect(() => {
    if (labelDetailData) {
      console.log('获取到标注详情:', labelDetailData);
      // 如果需要对标注详情数据进行特殊处理，可以在这里进行
    }
  }, [labelDetailData]);

  // 进入编辑模式
  const handleEnterEditMode = () => {
    if (!mapInstance || !selectedFeature || !selectedLabelId) return;
    
    setIsEditMode(true);
    
    // 添加修改交互
    const modifyInteraction = new Modify({
      features: new Collection([selectedFeature]),
    });
    
    mapInstance.addInteraction(modifyInteraction);
    modifyInteractionRef.current = modifyInteraction;
    
    message.info('已进入编辑模式，您可以拖动标注上的锚点进行编辑。');
  };
  
  // 保存编辑的标注
  const handleSaveLabel = (values: any) => {
    if (!selectedFeature || !selectedLabelId || !labelDetailData?.data) return;
    
    // 获取更新后的几何数据
    const geometry = selectedFeature.getGeometry();
    if (!geometry) return;
    
    const geoJsonWriter = new GeoJSON();
    const geometryJson = geoJsonWriter.writeGeometryObject(geometry, {
      featureProjection: 'EPSG:3857',
      dataProjection: 'EPSG:4326',
    });
    
    // 准备更新的数据
    try {
      // 构建符合格式要求的dataJson
      const dataJsonObj = {
        type: 'Feature',
        geometry: geometryJson,
        properties: {
          title: values.name,
          description: values.remark || '',
          color: values.strokeColor || '#4285F4',
          lineWidth: Number(values.strokeWeight) || 2,
        }
      };
      
      // 准备API调用参数
      const updateData: {
        labelName: string;
        labelRemark: string;
        dataJson: any; // 修改为直接传对象，而非字符串
        userId: string;
        quartelId?: number;
      } = {
        labelName: values.name,
        labelRemark: values.remark || '',
        dataJson: dataJsonObj, // 直接传递对象，不用JSON.stringify
        userId: String(userId),
      };
      
      // 如果有林班ID，添加到请求中
      if (values.quartelId) {
        updateData.quartelId = values.quartelId;
      }
      
      console.log('更新标注数据:', updateData);
      
      // 调用API更新标注
      updateLabelMutate({ id: selectedLabelId, data: updateData }, {
        onSuccess: () => {
          message.success('标注更新成功！');
          
          // 退出编辑模式
          if (modifyInteractionRef.current && mapInstance) {
            mapInstance.removeInteraction(modifyInteractionRef.current);
            modifyInteractionRef.current = null;
          }
          setIsEditMode(false);
          
          // 刷新标注数据
          queryClient.invalidateQueries('getAllLabels');
          queryClient.invalidateQueries(['getLabelById', selectedLabelId]);
        },
        onError: (error) => {
          console.error('更新标注失败:', error);
          message.error('更新标注失败，请重试');
        }
      });
    } catch (error) {
      console.error('准备更新数据失败:', error);
      message.error('更新标注失败，数据格式错误');
    }
  };
  
  // 当编辑模式或选中特征变化时，处理交互状态
  useEffect(() => {
    // 如果退出编辑模式，移除修改交互
    if (!isEditMode && modifyInteractionRef.current && mapInstance) {
      mapInstance.removeInteraction(modifyInteractionRef.current);
      modifyInteractionRef.current = null;
    }
    
    // 如果没有选中的特征，退出编辑模式
    if (!selectedFeature && isEditMode) {
      setIsEditMode(false);
    }
  }, [isEditMode, selectedFeature, mapInstance]);

  // 删除标注
  const handleDeleteLabel = () => {
    if (!selectedLabelId) return;
    
    console.log('删除标注:', selectedLabelId);
    
    // 调用删除标注API
    deleteLabelMutate(selectedLabelId, {
      onSuccess: () => {
        message.success('标注删除成功！');
        
        // 关闭属性面板
        setSelectedFeature(null);
        setSelectedLabelId(null);
        setIsEditMode(false);
        
        // 移除地图上的标注
        if (selectedFeature && mapInstance) {
          const labelsLayer = labelsLayerRef.current;
          if (labelsLayer && labelsLayer.getSource()) {
            labelsLayer.getSource().removeFeature(selectedFeature);
          }
        }
        
        // 刷新标注列表
        queryClient.invalidateQueries('getAllLabels');
      },
      onError: (error) => {
        console.error('删除标注失败:', error);
        message.error('删除标注失败，请重试');
      }
    });
  };

  // 根据林班ID过滤标注
  const filterLabelsByLinban = (linbanId: number) => {
    if (!labelsData?.data || !labelsLayerRef.current) return;
    
    // 过滤出属于指定林班的标注
    const filteredLabels = labelsData.data.filter((label: any) => 
      label.quartelId === linbanId
    );
    
    console.log(`筛选林班(ID:${linbanId})标注，共${filteredLabels.length}条`);
    
    // 显示筛选后的标注
    displayAllLabels(filteredLabels);
  };
  
  // 显示林班边界
  const showLinbanBoundary = (linbanId: number) => {
    if (!mapInstance) return;
    
    // 创建或获取林班边界图层
    if (!linbanBoundaryLayerRef.current) {
      const boundarySource = new VectorSource();
      const boundaryLayer = new VectorLayer({
        source: boundarySource,
        style: new Style({
          stroke: new Stroke({
            color: '#1890ff',
            width: 3,
            lineDash: [5, 5]
          }),
          fill: new Fill({
            color: 'rgba(24, 144, 255, 0.1)'
          })
        }),
        properties: {
          name: '林班边界图层'
        }
      });
      
      mapInstance.addLayer(boundaryLayer);
      linbanBoundaryLayerRef.current = boundaryLayer;
    }
    
    // 清除现有的边界
    const boundarySource = linbanBoundaryLayerRef.current.getSource();
    boundarySource.clear();
    
    // TODO: 从API获取林班边界数据并显示
    // 这里应该通过API获取林班边界数据，目前简化处理
    // 可以实现一个新的API来获取林班的地理数据
    
    // 示例：假设我们有了林班边界的GeoJSON数据
    // fetchLinbanBoundary(linbanId).then(boundaryData => {
    //   if (boundaryData && linbanBoundaryLayerRef.current) {
    //     const features = new GeoJSON().readFeatures(boundaryData, {
    //       featureProjection: 'EPSG:3857'
    //     });
    //     
    //     linbanBoundaryLayerRef.current.getSource().addFeatures(features);
    //     
    //     // 缩放到林班区域
    //     mapInstance.getView().fit(linbanBoundaryLayerRef.current.getSource().getExtent(), {
    //       padding: [50, 50, 50, 50],
    //       duration: 1000
    //     });
    //   }
    // });
    
    // 提示用户当前筛选模式
    message.info(`已筛选显示林班ID: ${linbanId} 的内容`);
  };
  
  // 清除林班边界
  const clearLinbanBoundary = () => {
    if (!linbanBoundaryLayerRef.current) return;
    
    // 清除边界图层中的要素
    linbanBoundaryLayerRef.current.getSource().clear();
    
    message.info('已清除林班筛选');
  };

  // Add share label functionality
  const handleShareLabel = () => {
    if (selectedFeature && labelDetailData) {
      setSelectedLabelForShare({
        feature: selectedFeature,
        labelInfo: labelDetailData
      });
      setShareModalVisible(true);
    }
  };

  // Handle share label submission
  const handleShareLabelSubmit = async (selectedUsers: any[]) => {
    if (!selectedLabelForShare || selectedUsers.length === 0) {
      return;
    }

    setShareLoading(true);
    
    try {
      const { feature, labelInfo } = selectedLabelForShare;
      const result = await sendLocationMessageToUsers(selectedUsers, feature, labelInfo);
      
      if (result.success) {
        message.success(result.message || '分享成功');
        setShareModalVisible(false);
      } else {
        message.error(result.message || '分享失败');
      }
    } catch (error) {
      console.error('分享标注失败:', error);
      message.error('分享标注失败，请重试');
    } finally {
      setShareLoading(false);
    }
  };

  // Close share modal
  const handleCloseShareModal = () => {
    setShareModalVisible(false);
    setSelectedLabelForShare(null);
  };

  // 处理URL参数中的位置信息
  useEffect(() => {
    if (!mapInstance) return;
    
    const lat = searchParams.get('lat');
    const lng = searchParams.get('lng');
    const labelId = searchParams.get('labelId');
    const labelName = searchParams.get('labelName') || '位置标记';
    
    if (lat && lng) {
      const latitude = parseFloat(lat);
      const longitude = parseFloat(lng);
      
      if (!isNaN(latitude) && !isNaN(longitude)) {
        // 创建一个标记点
        const position = fromLonLat([longitude, latitude]);
        
        // 创建标记要素
        const locationFeature = new Feature({
          geometry: new Point(position),
          name: labelName,
          // 如果有标注ID，尝试获取详细信息
          labelId: labelId || undefined
        });
        
        // 设置标记样式
        locationFeature.setStyle(new Style({
          image: new CircleStyle({
            radius: 10,
            fill: new Fill({
              color: '#3370ff'
            }),
            stroke: new Stroke({
              color: '#ffffff',
              width: 2
            })
          }),
          text: new Text({
            text: labelName,
            offsetY: -15,
            fill: new Fill({
              color: '#333'
            }),
            stroke: new Stroke({
              color: '#ffffff',
              width: 3
            })
          })
        }));
        
        // 查找或创建标记图层
        let markersLayer: any = null;
        mapInstance.getLayers().forEach((layer: any) => {
          if (layer.get('name') === '共享位置图层') {
            markersLayer = layer;
          }
        });
        
        if (!markersLayer) {
          const source = new VectorSource();
          markersLayer = new VectorLayer({
            source,
            properties: {
              name: '共享位置图层'
            }
          });
          mapInstance.addLayer(markersLayer);
        }
        
        // 清除之前的共享位置点
        markersLayer.getSource().clear();
        
        // 添加新的位置标记
        markersLayer.getSource().addFeature(locationFeature);
        
        // 将地图视图中心移到标记点位置
        mapInstance.getView().animate({
          center: position,
          zoom: 14,
          duration: 1000
        });
        
        // 如果有标注ID且该标注存在，显示标注详情
        if (labelId) {
          // 设置选中的标注ID，触发查询
          setSelectedLabelId(labelId);
          // 选中此标记以显示属性面板
          setSelectedFeature(locationFeature);
        }
      }
    }
  }, [mapInstance, searchParams]);

  return (
    <div style={{ 
      height: '100vh', // 明确设置高度为视口的100%
      width: '100%', 
      display: 'flex',
      overflow: 'hidden', // 防止可能的滚动条影响布局
      position: 'relative' // 确保子绝对定位元素相对于此容器
    }}>
      {/* 左侧侧边栏 - 使用抽离的SidePanel组件 */}
      <div className={`side-panel-container ${isPanelVisible ? 'visible' : ''}`}>
        <SidePanel
          mapInstance={mapInstance}
          onDrawSelect={handleDrawSelect}
          onPlotSelect={handlePlotSelect}
          onUserSelect={handleUserSelect}
          onFavoriteSelect={handleFavoriteSelect}
        />
      </div>
      
      {/* 右侧地图 */}
      <div style={{ 
        flex: 1, 
        height: '100%', 
        position: 'relative',
        overflow: 'hidden'
      }}>
        {isClient ? (
          <>
            <div style={{ position: 'relative', width: '100%', height: '100%' }}>
              <MapComponent setMapInstance={setMapInstance} drawnItems={drawnItems} />
              {mapInstance && <LayerSwitcher map={mapInstance} />}
              {mapInstance && <MapToolbar map={mapInstance} />}
              {mapInstance && <CoordinateDisplay map={mapInstance} />}
              {selectedUserInfo && (
                <UserInfoPanel key={selectedUserInfo.user.id}
                  user={selectedUserInfo.user}
                  coordinate={selectedUserInfo.coordinate}
                  onClose={() => { playback.clearTrajectory(); setSelectedUserInfo(null); }}
                  progress={playback.progress}
                  isPlaying={playback.isPlaying}
                  speed={playback.speed}
                  hasTrajectoryData={playback.hasTrajectoryData}
                  isLoadingTrajectory={playback.isLoadingTrajectory}
                  currentTime={playback.currentTime}
                  playbackTime={playback.playbackTime}
                  onPlay={playback.play}
                  onPause={playback.pause}
                  onSeek={playback.seek}
                  onSpeedChange={playback.setSpeed}
                  onQueryTrajectory={playback.queryTrajectory}
                />
              )}
            </div>
          </>
        ) : (
          <div style={{ flex: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            加载中...
          </div>
        )}
        {selectedFeature && (
            <PropertiesPanel 
                feature={selectedFeature}
                onClose={() => {
                  setSelectedFeature(null); 
                  setSelectedLabelId(null);
                  setIsEditMode(false);
                }}
                onChange={handlePropertyChange}
                labelDetail={selectedLabelId ? labelDetailData?.data : null}
                isLabelDetailLoading={isLabelDetailLoading}
                onEdit={handleEnterEditMode}
                onSave={handleSaveLabel}
                isEditMode={isEditMode}
                editLoading={isUpdatingLabel}
                onDeleteLabel={handleDeleteLabel}
                onShareLabel={handleShareLabel}
            />
        )}
        {drawingFeature && (
          <PropertiesPanel
            feature={drawingFeature}
            onClose={() => setDrawingFeature(null)}
            onChange={handlePropertyChange}
            onSaveDrawing={handleSaveDrawing}
            onDeleteDrawing={handleCancelDrawing}
            loading={isCreatingLabel || isAddingFavorite}
          />
        )}
      </div>
      
      {/* 版权信息 - 移动到外层容器中，确保显示 */}
      <CopyrightInfo />

      {/* Add Share Modal */}
      <ShareLabelModal
        visible={shareModalVisible}
        onClose={handleCloseShareModal}
        onShare={handleShareLabelSubmit}
        loading={shareLoading}
        title={`分享标注: ${selectedLabelForShare?.labelInfo?.labelName || '未命名标注'}`}
      />
    </div>
  );
};

export default Home; 
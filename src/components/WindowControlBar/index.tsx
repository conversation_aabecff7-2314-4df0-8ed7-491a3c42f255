import { Platform } from "@openim/wasm-client-sdk";
import { useKeyPress } from "ahooks";

import win_close from "@/assets/images/topSearchBar/win_close.png";
import win_max from "@/assets/images/topSearchBar/win_max.png";
import win_min from "@/assets/images/topSearchBar/win_min.png";

const WindowControlBar = () => {
  useKeyPress("esc", () => {
    window.electronAPI?.ipcInvoke("minimizeWindow");
  });

  // 直接返回null，移除按钮
    return null;
};

export default WindowControlBar;

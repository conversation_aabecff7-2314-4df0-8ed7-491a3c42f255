import React from 'react';

const Logo = ({ width = 32, height = 32 }) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 120 120"
    xmlns="http://www.w3.org/2000/svg"
    aria-label="林场管理系统Logo"
  >
    <defs>
      <linearGradient id="trunkGradient" x1="0%" y1="0%" x2="0%" y2="100%">
        <stop offset="0%" style={{ stopColor: '#5d4037', stopOpacity: 1 }} />
        <stop offset="100%" style={{ stopColor: '#795548', stopOpacity: 1 }} />
      </linearGradient>
      <radialGradient id="foliageGradient" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
        <stop offset="0%" style={{ stopColor: '#4CAF50', stopOpacity: 1 }} />
        <stop offset="100%" style={{ stopColor: '#388E3C', stopOpacity: 1 }} />
      </radialGradient>
    </defs>
    
    {/* Background Circle */}
    <circle cx="60" cy="60" r="58" fill="#E8F5E9" />
    
    {/* Tree Trunk */}
    <rect x="55" y="70" width="10" height="40" fill="url(#trunkGradient)" rx="2" />

    {/* Tree Foliage - a stylized pine tree */}
    <path 
      d="M60 10 L85 50 L70 50 L95 80 L25 80 L50 50 L35 50 Z"
      fill="url(#foliageGradient)"
    />
    
    {/* Ground */}
    <path d="M10 110 Q 60 95, 110 110" stroke="#4CAF50" strokeWidth="4" fill="transparent" />
  </svg>
);

export default Logo; 
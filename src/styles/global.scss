:root {
  --full-height: 100%;
  --chat-bubble: #f4f5f7;
  --chat-bubble-sender: #cce7fe;
  --base-black: #0c1c33;
  --primary: #0089ff;
  --primary-active: #f3f8ff;
  --top-search-bar: #0289fa;
  --sub-text: #8e9ab0;
  --gap-text: #e8eaef;
  --warn-text: #ff381f;
  --moment-text: #6085b1;
  --searchbar-height: 2.5rem;
}

@media only screen and (max-width: 600px) {
  .no-mobile {
    display: none;
  }
}

html {
  height: var(--full-height);

  font-family: "Noto Sans SC", "SF Pro SC", "SF Pro Text", "SF Pro Icons", "PingFang SC",
    "Helvetica Neue", "Helvetica", "Arial", sans-serif;

  div {
    color: var(--base-black);
  }
}

body,
#root {
  height: var(--full-height);
  overflow: hidden;
  user-select: none;
}

::-webkit-scrollbar {
  width: 6px;
  background-color: transparent;
}
::-webkit-scrollbar-track {
  background: transparent;
}
::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: #b7bdcb;
  box-shadow: 4px 4px 15px rgba(112, 124, 151, 0.05),
    2px 2px 10px rgba(112, 124, 151, 0.1), 1px 1px 50px rgba(112, 124, 151, 0.15);
}
::-webkit-scrollbar-thumb:hover {
  border-radius: 3px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: rgba(245, 238, 238, 1);
}

#chat-list {
  &::-webkit-scrollbar {
    width: 8px;
  }
}

.no-scrollbar {
  &::-webkit-scrollbar {
    display: none;
  }
}

.emojione {
  width: 16px !important;
  height: 16px !important;
  display: inline-block;
}

.image-el {
  vertical-align: bottom;
  display: inline-block;
  max-width: 30vw;
  max-height: 15vh;
  object-fit: contain;
  cursor: pointer;
}

.at-el {
  display: inline-block;
  color: var(--primary);
  width: fit-content;
}

.link-el {
  color: var(--primary);
  cursor: pointer;
}

.member-el {
  margin: 0 4px;
}

.app-drag {
  -webkit-app-region: drag;
}

.app-no-drag {
  -webkit-app-region: no-drag;
}

.text-break {
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
}

#scrollableDiv {
  position: relative;
}

.growable::before {
  content: "";
  flex-grow: 1;
}

#video_player {
  position: unset !important;
}

.video-img {
  position: relative;
  display: inline-block;
}

.video-image-mask:hover {
  opacity: 1;
}

.video-image-mask {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  background: rgba(0, 0, 0, 0.5);
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.3s;
  inset: 0;
}

.no-scrollbar {
  ::-webkit-scrollbar {
    width: 0px;
    display: none;
  }
}

img {
  -webkit-user-drag: none;
}

.custom-form-item > label {
  color: var(--sub-text) !important;
  margin-bottom: 4px !important;
}

.lk-participant-media-video {
  object-fit: contain !important;
}

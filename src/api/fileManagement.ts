import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';

const BASE_URL = import.meta.env.VITE_LINBANAPI_URL as string;

const request = axios.create({
  baseURL: BASE_URL,
  timeout: 60000, // 增加超时时间以支持大文件上传
});

// 添加请求拦截器
request.interceptors.request.use(
  (config) => {
    config.headers.operationID = uuidv4();
    return config;
  },
  (err) => Promise.reject(err),
);

// 添加响应拦截器处理统一响应格式
request.interceptors.response.use(
  (res) => {
    // 如果是二进制数据（文件下载），直接返回
    if (res.config.responseType === 'blob') {
      return res;
    }

    // 处理统一的API响应格式
    if (res.data && typeof res.data === 'object') {
      if (res.data.code === 0) {
        return res.data;
      } else {
        return Promise.reject(res.data);
      }
    }
    return res.data;
  },
  (err) => {
    console.error('文件管理API请求失败:', err);
    return Promise.reject(err);
  },
);

// 根据新API文档的类型定义

// 初始化分片上传参数
export interface ChunkUploadInitParams {
  fileName: string;
  fileMd5: string;
  totalSize: number;
  chunkSize: number;
  fileType: string;
  path?: string;
}

// 初始化分片上传响应
export interface ChunkUploadInitResponse {
  uploadId: string;
  needUpload: boolean;
  uploadedChunks: number[];
  totalChunks: number;
  fileUrl?: string;
  message: string;
}

// 分片上传参数
export interface ChunkUploadParams {
  uploadId: string;
  chunkNumber: number;
  chunkFile: Blob;
  chunkMd5?: string;
}

// 分片上传响应
export interface ChunkUploadResponse {
  chunkNumber: number;
  success: boolean;
  message: string;
  uploadedChunks: number;
  totalChunks: number;
  progress: number;
}

// 合并分片参数
export interface ChunkMergeParams {
  uploadId: string;
  fileMd5: string;
}

// 合并分片响应
export interface ChunkMergeResponse {
  success: boolean;
  fileUrl: string;
  fileSize: number;
  message: string;
}

// 上传进度响应
export interface UploadProgressResponse {
  uploadedChunks: number;
  totalChunks: number;
  progress: number;
  success: boolean;
  message: string;
}

// 文件信息接口（保持兼容性）
export interface FileInfo {
  id: number;
  fileName: string;
  originalName: string;
  filePath: string;
  fileSize: number;
  fileType?: string;
  fileMd5: string;
  fileExtension?: string;
  mimeType?: string;
  uploadStatus: number; // 0-上传中，1-上传完成，2-上传失败
  totalChunks?: number;
  uploadedChunks?: number;
  linbanId?: number;
  linbanName?: string;
  collector?: string;
  collectDate?: string;
  tags?: string;
  description?: string;
  createTime: string;
  updateTime: string;
  creator?: string;
  updater?: string;
}

// 分页查询参数
export interface FileQueryParams {
  pageNo?: number;
  pageSize?: number;
  originalName?: string;
  fileType?: string;
  uploadStatus?: number;
  linbanId?: number;
  linbanName?: string;
  collector?: string;
  collectDate?: string[]; // 日期范围
  createTime?: string[]; // 创建时间范围
}

// 分页查询响应
export interface FilePageResponse {
  total: number;
  list: FileInfo[];
}

// 普通文件上传参数（保持兼容性）
export interface FileUploadParams {
  file: File;
  linbanId?: number;
  linbanName?: string;
  collector?: string;
  collectDate?: string;
  tags?: string;
  description?: string;
}

/**
 * 分页查询文件列表
 */
export const getFileList = (params: FileQueryParams) => {
  return request.get<FilePageResponse>('/user/file/page', { params });
};

/**
 * 获取文件详细信息
 */
export const getFileInfo = (id: number) => {
  return request.get<FileInfo>('/user/file/get', { params: { id } });
};

/**
 * 普通文件上传
 */
export const uploadFile = (params: FileUploadParams) => {
  const formData = new FormData();
  formData.append('file', params.file);
  if (params.linbanId) formData.append('linbanId', params.linbanId.toString());
  if (params.linbanName) formData.append('linbanName', params.linbanName);
  if (params.collector) formData.append('collector', params.collector);
  if (params.collectDate) formData.append('collectDate', params.collectDate);
  if (params.tags) formData.append('tags', params.tags);
  if (params.description) formData.append('description', params.description);

  return request.post<number>('/user/file/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

// ===== 新的分片上传API =====

/**
 * 初始化分片上传
 */
export const initChunkUpload = (params: ChunkUploadInitParams) => {
  return request.post<ChunkUploadInitResponse>('/infra/file/chunk/init', params);
};

/**
 * 上传文件分片
 */
export const uploadChunk = (params: ChunkUploadParams) => {
  const formData = new FormData();
  formData.append('uploadId', params.uploadId);
  formData.append('chunkNumber', params.chunkNumber.toString());
  formData.append('chunkFile', params.chunkFile);
  if (params.chunkMd5) {
    formData.append('chunkMd5', params.chunkMd5);
  }

  return request.post<ChunkUploadResponse>('/infra/file/chunk/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

/**
 * 合并文件分片
 */
export const mergeChunks = (params: ChunkMergeParams) => {
  return request.post<ChunkMergeResponse>('/infra/file/chunk/merge', params);
};

/**
 * 取消分片上传
 */
export const cancelChunkUpload = (uploadId: string) => {
  return request.delete<boolean>('/infra/file/chunk/cancel', {
    params: { uploadId }
  });
};

/**
 * 获取上传进度
 */
export const getUploadProgress = (uploadId: string) => {
  return request.get<UploadProgressResponse>('/infra/file/chunk/progress', {
    params: { uploadId }
  });
};

// ===== 兼容旧接口（保持向后兼容）=====

/**
 * 创建分块上传会话（旧接口，保持兼容性）
 */
export const createUploadSession = (params: any) => {
  console.warn('createUploadSession 接口已废弃，请使用 initChunkUpload');
  return Promise.reject(new Error('接口已废弃'));
};

/**
 * 获取上传会话状态（旧接口，保持兼容性）
 */
export const getUploadSessionStatus = (sessionId: string) => {
  console.warn('getUploadSessionStatus 接口已废弃，请使用 getUploadProgress');
  return Promise.reject(new Error('接口已废弃'));
};

/**
 * 取消上传会话（旧接口，保持兼容性）
 */
export const cancelUploadSession = (sessionId: string) => {
  console.warn('cancelUploadSession 接口已废弃，请使用 cancelChunkUpload');
  return Promise.reject(new Error('接口已废弃'));
};

/**
 * 检查文件MD5（秒传功能）
 */
export const checkFileMd5 = (md5: string) => {
  return request.get<FileInfo | null>('/user/file/check-md5', { params: { md5 } });
};

/**
 * 删除文件
 */
export const deleteFile = (id: number) => {
  return request.delete<boolean>('/user/file/delete', { params: { id } });
};

/**
 * 下载文件
 */
export const downloadFile = (id: number) => {
  return request.get(`/user/file/download/${id}`, {
    responseType: 'blob',
  });
};

/**
 * 预览文件
 */
export const previewFile = (id: number) => {
  return request.get(`/user/file/preview/${id}`, {
    responseType: 'blob',
  });
};

/**
 * 获取文件预览URL
 */
export const getPreviewUrl = (id: number) => {
  return request.get<string>(`/user/file/preview-url/${id}`);
};

// 兼容旧接口的类型定义（保持向后兼容）
export interface ChunkUploadResponse {
  success: boolean;
  message: string;
  chunkNumber: number;
  uploadedChunks: number;
  totalChunks: number;
  canMerge: boolean;
}

export interface UploadStatusResponse {
  uploaded: boolean;
  uploadedChunks: number;
  totalChunks: number;
  uploadedChunkNumbers: number[];
}

export interface MergeChunksResponse {
  success: boolean;
  message: string;
  fileId: number;
}

// 兼容旧接口的参数类型（保持向后兼容）
export interface ChunkUploadParams_Old {
  chunk: Blob;
  fileMd5: string;
  fileName: string;
  fileSize: number;
  chunkNumber: number;
  totalChunks: number;
  chunkSize: number;
  collectDate: string;
  linbanId: string;
  linbanName: string;
  collector: string;
  remark?: string;
}

// 兼容旧版本的函数（暂时保留，方便迁移）
export const checkUploadStatus = async (fileMd5: string): Promise<{ data: UploadStatusResponse }> => {
  // 这个接口在新版本中不存在，返回模拟数据
  console.warn('checkUploadStatus 接口已废弃，请使用 getUploadSessionStatus');
  return {
    data: {
      uploaded: false,
      uploadedChunks: 0,
      totalChunks: 0,
      uploadedChunkNumbers: []
    }
  };
}; 
import axios from "axios";
import { useMutation, useQuery } from 'react-query';
import { errorHandle } from './errorHandle';
import { format, subDays } from 'date-fns';
import { getLinbanToken, setLinbanToken, setLinbanUserID } from '@/utils/storage';
import { v4 as uuidv4 } from 'uuid';

const BASE_URL = import.meta.env.VITE_LINBANAPI_URL as string;

const linbanRequest = axios.create({
  baseURL: BASE_URL,
  timeout: 25000,
});

// 添加认证拦截器
linbanRequest.interceptors.request.use(
  async (config) => {
    const token = await getLinbanToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      config.headers.token = token;
    }
    config.headers.operationID = uuidv4();
    return config;
  },
  (err) => Promise.reject(err),
);

export interface LoginIMParams {
  phoneNumber: string;
  password: string;
}

export interface LoginIMResponse<T = any> {
  code: number;
  data: T;
  msg: string;
}

/**
 * 业务系统 IM 登录接口
 */
export const loginIM = (params: LoginIMParams) =>
  linbanRequest.post<LoginIMResponse>('app-api/linban/user/login-im', params, {
    headers: {
      'Content-Type': 'application/json',
    },
  }).then(res => res.data);

/**
 * React Query 钩子：业务系统 IM 登录
 */
export const useLoginIM = () =>
  useMutation((params: LoginIMParams) => loginIM(params), {
    onError: errorHandle,
  });

export interface GetAllLinbanParams {
  userId: string;
}

export interface GetFavoritesParams {
  pageNo: number;
  pageSize: number;
  userId: string;
}

/**
 * 获取林班列表
 */
export const getAllLinban = (params: GetAllLinbanParams) =>
  linbanRequest
    .get<LoginIMResponse<any>>('app-api/linban/user/get-all-linban', { params })
    .then(res => res.data);

/**
 * 获取收藏夹列表
 */
export const getFavorites = (params: GetFavoritesParams) =>
  linbanRequest
    .post<LoginIMResponse<any>>('/app-api/linban/label-folder/page-label', params, {
      headers: {
        'Content-Type': 'application/json',
      },
    })
    .then(res => res.data);

/**
 * 获取用户详细信息 (包含最后位置)
 */
export const getLastPosition = (userId: string) =>
  linbanRequest
    .get<LoginIMResponse<any>>('/app-api/linban/user/get', { params: { id: userId } })
    .then(res => res.data);

/**
 * React Query 钩子：获取林班列表
 */
export const useGetAllLinban = (userId: string) =>
  useQuery(['getAllLinban', userId], () => getAllLinban({ userId }), {
    onError: errorHandle,
    enabled: !!userId,
  });

/**
 * 新增接口和类型
 */
export interface GetAllInLinbanParams {
  id: number;
  quartelName: string;
}

/**
 * 获取指定林班下的所有人员
 */
export const getAllInLinban = (params: GetAllInLinbanParams) =>
  linbanRequest
    .get<LoginIMResponse<any>>('app-api/linban/user/get-all-in-linban', { params })
    .then(res => res.data);

/**
 * React Query 钩子：获取指定林班下的所有人员
 * 使用 useMutation 因为这是在用户交互后触发的查询
 */
export const useGetAllInLinban = () =>
  useMutation((params: GetAllInLinbanParams) => getAllInLinban(params), {
    onError: errorHandle,
  });

/**
 * React Query 钩子：获取收藏夹列表
 */
export const useGetFavorites = (userId: string) =>
  useQuery(
    ['getFavorites', userId],
    () => getFavorites({ pageNo: 1, pageSize: 1000, userId }),
    {
      onError: errorHandle,
      enabled: !!userId,
    },
  );

/**
 * React Query 钩子：获取用户最后位置
 */
export const useGetLastPosition = (
  userId: string,
  options?: { enabled?: boolean },
) =>
  useQuery(['getLastPosition', userId], () => getLastPosition(userId), {
    onError: errorHandle,
    staleTime: 1000 * 60, // 1分钟内数据被认为是新鲜的
    ...options, // 将外部选项合并进来
  });

/**
 * 获取用户历史轨迹
 */
export const getTrajectory = (userId: string, startTime?: string, endTime?: string) => {
  const now = new Date();
  const defaultStartTime = format(subDays(now, 1), 'yyyy-MM-dd HH:mm:ss');
  const defaultEndTime = format(now, 'yyyy-MM-dd HH:mm:ss');

  // 手动构建查询字符串
  const params = new URLSearchParams();
  params.append('userId', userId);
  params.append('createTime', startTime || defaultStartTime);
  params.append('createTime', endTime || defaultEndTime);

  return linbanRequest
    .get<LoginIMResponse<any>>(`/app-api/linban/user-track/list?${params.toString()}`)
    .then(res => res.data);
};

export interface AddFavoriteParams {
  labelId: string;
  userId: string;
  type: string;
  quartelId?: number;
}

export interface CreateLabelParams {
  labelName: string;
  labelRemark?: string;
  quartelId?: number;
  dataJson: any; // GeoJSON 对象或字符串
  type: string; // 'Point', 'LineString', 'Polygon', etc.
  userId: string;
}

/**
 * 创建标记点
 */
export const createLabel = (params: CreateLabelParams) =>
  linbanRequest.post<LoginIMResponse>('/app-api/linban/label/create', params, {
    headers: {
      'Content-Type': 'application/json',
    },
  }).then(res => res.data);

/**
 * React Query 钩子：创建标记点
 */
export const useCreateLabel = () =>
  useMutation((params: CreateLabelParams) => createLabel(params), {
    onError: errorHandle,
  });

/**
 * 添加到收藏夹
 */
export const addFavorite = (params: AddFavoriteParams) =>
  linbanRequest.post<LoginIMResponse>('/app-api/linban/label-folder/create', params, {
    headers: {
      'Content-Type': 'application/json',
    },
  }).then(res => res.data);

/**
 * React Query 钩子：添加到收藏夹
 */
export const useAddFavorite = () =>
  useMutation((params: AddFavoriteParams) => addFavorite(params), {
    onError: errorHandle,
  });

/**
 * 获取所有标注列表
 */
export const getAllLabels = (userId: string) =>
  linbanRequest
    .get<LoginIMResponse<any>>('/app-api/linban/label/list', { params: { userId } })
    .then(res => res.data);

/**
 * React Query 钩子：获取所有标注列表
 */
export const useGetAllLabels = (userId: string) =>
  useQuery(['getAllLabels', userId], () => getAllLabels(userId), {
    onError: errorHandle,
    enabled: !!userId,
  });

/**
 * 获取单个标注详情
 */
export const getLabelById = (id: string) =>
  linbanRequest
    .get<LoginIMResponse<any>>('/app-api/linban/label/get', { params: { id } })
    .then(res => res.data);

/**
 * React Query 钩子：获取单个标注详情
 */
export const useGetLabelById = (id: string, options?: { enabled?: boolean }) =>
  useQuery(['getLabelById', id], () => getLabelById(id), {
    onError: errorHandle,
    enabled: !!id && (options?.enabled !== false),
  });

/**
 * 更新标注信息
 */
export const updateLabel = (id: string, data: any) =>
  linbanRequest.put<LoginIMResponse>(`/app-api/linban/label/update/${id}`, data, {
    headers: {
      'Content-Type': 'application/json',
    },
  }).then(res => res.data);

/**
 * React Query 钩子：更新标注信息
 */
export const useUpdateLabel = () =>
  useMutation((params: { id: string; data: any }) => updateLabel(params.id, params.data), {
    onError: errorHandle,
  });

/**
 * 删除标注
 */
export const deleteLabel = (id: string) =>
  linbanRequest.delete<LoginIMResponse>(`/app-api/linban/label/delete`, {
    params: { id },
  }).then(res => res.data);

/**
 * React Query 钩子：删除标注
 */
export const useDeleteLabel = () =>
  useMutation((id: string) => deleteLabel(id), {
    onError: errorHandle,
  }); 
import { CbEvents, MessageType } from "@openim/wasm-client-sdk";
import {
  GroupItem,
  MessageItem,
  RtcInvite,
  WSEvent,
} from "@openim/wasm-client-sdk/lib/types/entity";
import { Popover, Select, Tooltip, Button } from "antd";
import i18n, { t } from "i18next";
import { useCallback, useEffect, useRef, useState } from "react";

import { getBusinessUserInfo } from "@/api/login";
import add_friend from "@/assets/images/topSearchBar/add_friend.png";
import add_group from "@/assets/images/topSearchBar/add_group.png";
import create_group from "@/assets/images/topSearchBar/create_group.png";
import show_more from "@/assets/images/topSearchBar/show_more.png";
import add_forest from "@/assets/images/topSearchBar/create_group.png";
import WindowControlBar from "@/components/WindowControlBar";
import Logo from "@/components/Logo";
import { CustomType } from "@/constants";
import { OverlayVisibleHandle } from "@/hooks/useOverlayVisible";
import ChooseModal, { ChooseModalState } from "@/pages/common/ChooseModal";
import GroupCardModal from "@/pages/common/GroupCardModal";
import RtcCallModal from "@/pages/common/RtcCallModal";
import { InviteData } from "@/pages/common/RtcCallModal/data";
import UserCardModal, { CardInfo } from "@/pages/common/UserCardModal";
import { useContactStore, useUserStore } from "@/store";
import emitter, { OpenUserCardParams } from "@/utils/events";
import { useGetAllLinban } from "@/api/linban";
import MapToolbar from "@/pages/Home/components/MapToolbar";

import { IMSDK } from "../MainContentWrap";
import SearchUserOrGroup from "./SearchUserOrGroup";

type UserCardState = OpenUserCardParams & {
  cardInfo?: CardInfo;
};

const TopSearchBar = () => {
  const userCardRef = useRef<OverlayVisibleHandle>(null);
  const groupCardRef = useRef<OverlayVisibleHandle>(null);
  const chooseModalRef = useRef<OverlayVisibleHandle>(null);
  const searchModalRef = useRef<OverlayVisibleHandle>(null);
  const rtcRef = useRef<OverlayVisibleHandle>(null);
  const [chooseModalState, setChooseModalState] = useState<ChooseModalState>({
    type: "CRATE_GROUP",
  });
  const [userCardState, setUserCardState] = useState<UserCardState>();
  const [groupCardData, setGroupCardData] = useState<
    GroupItem & { inGroup?: boolean }
  >();
  const [actionVisible, setActionVisible] = useState(false);
  const [isSearchGroup, setIsSearchGroup] = useState(false);
  const [inviteData, setInviteData] = useState<InviteData>({} as InviteData);
  const [selectedLinban, setSelectedLinban] = useState<number | null>(null);
  const [mapInstance, setMapInstance] = useState(null);
  
  // 获取用户ID
  const userId = useUserStore(state => state.linBanUser.id);
  
  // 获取林班列表
  const { data: linbanData, isLoading } = useGetAllLinban(String(userId));
  
  // 处理林班选择变更
  const handleLinbanChange = (value: number) => {
    setSelectedLinban(value);
    // 触发林班筛选事件，让地图组件监听并处理
    emitter.emit("FILTER_BY_LINBAN", { linbanId: value });
  };

  // 订阅地图实例更新
  useEffect(() => {
    const handleMapInitialized = (map: any) => {
      setMapInstance(map);
    };
    
    emitter.on("MAP_INITIALIZED", handleMapInitialized);
    
    return () => {
      emitter.off("MAP_INITIALIZED", handleMapInitialized);
    };
  }, []);

  // 添加全局样式
  useEffect(() => {
    // 创建样式标签
    const styleEl = document.createElement('style');
    styleEl.innerHTML = `
      .ant-select-selection-placeholder {
        color: rgba(255, 255, 255, 0.7) !important;
      }
      .ant-select-selector {
        background-color: transparent !important;
        border: none !important;
        height: 26px !important;
        color: white !important;
        display: flex !important;
        align-items: center !important;
      }
      .ant-select-arrow {
        color: rgba(255, 255, 255, 0.7) !important;
      }
      .ant-select-clear {
        background-color: transparent !important;
        color: rgba(255, 255, 255, 0.7) !important;
      }
      .ant-select-selection-item {
        color: #000000 !important;
      }
    `;
    document.head.appendChild(styleEl);
    
    // 清理函数
    return () => {
      document.head.removeChild(styleEl);
    };
  }, []);

  useEffect(() => {
    const userCardHandler = (params: OpenUserCardParams) => {
      setUserCardState({ ...params });
      userCardRef.current?.openOverlay();
    };
    const chooseModalHandler = (params: ChooseModalState) => {
      setChooseModalState({ ...params });
      chooseModalRef.current?.openOverlay();
    };
    const callRtcHandler = (inviteData: InviteData) => {
      if (rtcRef.current?.isOverlayOpen) return;
      setInviteData(inviteData);
      rtcRef.current?.openOverlay();
    };
    const newMessageHandler = ({ data }: WSEvent<MessageItem[]>) => {
      if (rtcRef.current?.isOverlayOpen) return;
      let rtcInvite = undefined as undefined | RtcInvite;
      data.map((message) => {
        if (message.contentType === MessageType.CustomMessage) {
          const customData = JSON.parse(message.customElem!.data);
          if (customData.customType === CustomType.CallingInvite) {
            rtcInvite = customData.data;
          }
        }
      });
      if (rtcInvite) {
        getBusinessUserInfo([rtcInvite.inviterUserID]).then(({ data: { users } }) => {
          if (users.length === 0) return;
          setInviteData({
            invitation: rtcInvite,
            participant: {
              userInfo: {
                nickname: users[0].nickname,
                faceURL: users[0].faceURL,
                userID: users[0].userID,
                ex: "",
              },
            },
          });
          rtcRef.current?.openOverlay();
        });
      }
    };

    emitter.on("OPEN_USER_CARD", userCardHandler);
    emitter.on("OPEN_GROUP_CARD", openGroupCardWithData);
    emitter.on("OPEN_CHOOSE_MODAL", chooseModalHandler);
    emitter.on("OPEN_RTC_MODAL", callRtcHandler);
    IMSDK.on(CbEvents.OnRecvNewMessages, newMessageHandler);

    return () => {
      emitter.off("OPEN_USER_CARD", userCardHandler);
      emitter.off("OPEN_GROUP_CARD", openGroupCardWithData);
      emitter.off("OPEN_CHOOSE_MODAL", chooseModalHandler);
      emitter.off("OPEN_RTC_MODAL", callRtcHandler);
      IMSDK.off(CbEvents.OnRecvNewMessages, newMessageHandler);
    };
  }, []);

  const actionClick = (idx: number) => {
    switch (idx) {
      case 0:
      case 1:
        setIsSearchGroup(Boolean(idx));
        searchModalRef.current?.openOverlay();
        break;
      case 2:
        setChooseModalState({ type: "CRATE_GROUP" });
        chooseModalRef.current?.openOverlay();
        break;
      case 3:
        console.log('点击新增林班按钮');
        setTimeout(() => {
          emitter.emit("SHOW_MAP_TOOLBAR", { type: "polygon", isAnnotation: false });
        }, 10);
        break;
      default:
        break;
    }
    setActionVisible(false);
  };

  const openUserCardWithData = useCallback((cardInfo: CardInfo) => {
    searchModalRef.current?.closeOverlay();
    setUserCardState({
      userID: cardInfo.userID,
      cardInfo,
      isSelf: cardInfo.userID === useUserStore.getState().selfInfo.userID,
    });
    userCardRef.current?.openOverlay();
  }, []);

  const openGroupCardWithData = useCallback((group: GroupItem) => {
    searchModalRef.current?.closeOverlay();
    const inGroup = useContactStore
      .getState()
      .groupList.some((g) => g.groupID === group.groupID);
    setGroupCardData({ ...group, inGroup });
    groupCardRef.current?.openOverlay();
  }, []);

  return (
    <div className="no-mobile app-drag flex h-10 min-h-[40px] items-center bg-[var(--top-search-bar)] dark:bg-[#141414]">
      <div className="flex-none ml-4 mr-4 flex items-center">
        <Logo />
        <span className="text-lg font-bold text-[#ffffff] ml-2">{t("placeholder.forestManagementSystem")}</span>
      </div>
      <div className="flex w-full items-center justify-center">
        {/* 林班筛选下拉框 */}
        <Select
          className="app-no-drag mr-4 w-[180px]"
          placeholder="选择林班"
          loading={isLoading}
          onChange={handleLinbanChange}
          value={selectedLinban}
          allowClear
          showSearch
          optionFilterProp="label"
          options={(linbanData?.data || []).map((item: any) => ({
            label: item.quartelName,
            value: item.id,
          }))}
          dropdownStyle={{
            borderRadius: '6px',
          }}
          style={{
            backgroundColor: 'rgba(51, 112, 255, 0.8)',
            borderRadius: '6px',
            color: '#ffffff',
            height: '26px',
          }}
        />
        <div 
          className="app-no-drag flex h-[26px] w-1/3 items-center justify-center rounded-md bg-[#3370ff] cursor-pointer"
          onClick={() => {
            setIsSearchGroup(false);
            searchModalRef.current?.openOverlay();
          }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <span className="text-white text-sm">搜索</span>
        </div>
        <Popover
          content={<ActionPopContent actionClick={actionClick} />}
          arrow={false}
          title={null}
          trigger="click"
          placement="bottom"
          open={actionVisible}
          onOpenChange={(vis) => setActionVisible(vis)}
        >
          <img
            className="app-no-drag ml-4 cursor-pointer"
            width={20}
            src={show_more}
            alt=""
          />
        </Popover>
        
        {/* 添加地图绘制工具栏 */}
        <div className="app-no-drag ml-4">
          <MapToolbar 
            map={mapInstance} 
            position="top"
            style={{
              backgroundColor: 'rgba(51, 112, 255, 0.2)',
              borderRadius: '6px',
              padding: '2px',
            }}
          />
        </div>
      </div>
      <WindowControlBar />
      <UserCardModal ref={userCardRef} {...userCardState} />
      <GroupCardModal ref={groupCardRef} groupData={groupCardData} />
      <ChooseModal ref={chooseModalRef} state={chooseModalState} />
      <SearchUserOrGroup
        ref={searchModalRef}
        isSearchGroup={isSearchGroup}
        openUserCardWithData={openUserCardWithData}
        openGroupCardWithData={openGroupCardWithData}
      />
      <RtcCallModal ref={rtcRef} inviteData={inviteData} />
    </div>
  );
};

export default TopSearchBar;

const actionMenuList = [
  {
    idx: 0,
    title: t("placeholder.addFriends"),
    icon: add_friend,
  },
  {
    idx: 1,
    title: t("placeholder.addGroup"),
    icon: add_group,
  },
  {
    idx: 2,
    title: t("placeholder.createGroup"),
    icon: create_group,
  },
  {
    idx: 3,
    title: t("placeholder.addForestCompartment"),
    icon: add_forest,
  },
];

i18n.on("languageChanged", () => {
  actionMenuList[0].title = t("placeholder.addFriends");
  actionMenuList[1].title = t("placeholder.addGroup");
  actionMenuList[2].title = t("placeholder.createGroup");
  actionMenuList[3].title = t("placeholder.addForestCompartment");
});

const ActionPopContent = ({ actionClick }: { actionClick: (idx: number) => void }) => {
  return (
    <div className="p-1">
      {actionMenuList.map((action) => (
        <div
          className="flex cursor-pointer items-center rounded px-3 py-2 text-xs hover:bg-[var(--primary-active)]"
          key={action.idx}
          onClick={() => actionClick?.(action.idx)}
        >
          <img width={20} src={action.icon} alt="call_video" />
          <div className="ml-3">{action.title}</div>
        </div>
      ))}
    </div>
  );
};

# 文件上传功能使用指南

## 概述

本项目已重新实现文件上传功能，支持大文件分片上传、断点续传、并发控制等高级特性。新的上传系统基于最新的API文档设计，提供了更好的用户体验和更强的稳定性。

## 主要特性

### ✅ 分片上传
- 自动将大文件分割成小块进行上传
- 支持自定义分片大小
- 智能推荐最优分片大小

### ✅ 断点续传
- 上传中断后可以从断点继续
- 自动检测已上传的分片
- 避免重复上传

### ✅ 并发控制
- 支持多个分片并发上传
- 可配置最大并发数
- 优化网络带宽使用

### ✅ 进度监控
- 实时显示上传进度
- 显示上传速度
- 估算剩余时间
- 分片级别的进度跟踪

### ✅ 文件验证
- MD5校验确保文件完整性
- 重复文件检测（秒传）
- 文件类型验证
- 文件大小限制

### ✅ 用户交互
- 暂停/恢复上传
- 取消上传
- 错误重试
- 清理已完成任务

## API接口

### 1. 初始化分片上传
```typescript
POST /infra/file/chunk/init

// 请求参数
{
  fileName: string;
  fileMd5: string;
  totalSize: number;
  chunkSize: number;
  fileType: string;
  path?: string;
}

// 响应数据
{
  uploadId: string;
  needUpload: boolean;
  uploadedChunks: number[];
  totalChunks: number;
  fileUrl?: string;
  message: string;
}
```

### 2. 上传文件分片
```typescript
POST /infra/file/chunk/upload

// 请求参数 (multipart/form-data)
{
  uploadId: string;
  chunkNumber: number;
  chunkFile: Blob;
  chunkMd5?: string;
}

// 响应数据
{
  chunkNumber: number;
  success: boolean;
  message: string;
  uploadedChunks: number;
  totalChunks: number;
  progress: number;
}
```

### 3. 合并文件分片
```typescript
POST /infra/file/chunk/merge

// 请求参数
{
  uploadId: string;
  fileMd5: string;
}

// 响应数据
{
  success: boolean;
  fileUrl: string;
  fileSize: number;
  message: string;
}
```

### 4. 取消分片上传
```typescript
DELETE /infra/file/chunk/cancel?uploadId={uploadId}

// 响应数据
boolean
```

### 5. 获取上传进度
```typescript
GET /infra/file/chunk/progress?uploadId={uploadId}

// 响应数据
{
  uploadedChunks: number;
  totalChunks: number;
  progress: number;
  success: boolean;
  message: string;
}
```

## 使用方法

### 基本用法

```tsx
import FileUploader from '@/pages/data/components/FileUploader';

const MyComponent = () => {
  const handleUploadComplete = (fileId: string, fileUrl: string) => {
    console.log('上传完成:', fileUrl);
  };

  const handleUploadError = (fileId: string, error: string) => {
    console.error('上传失败:', error);
  };

  return (
    <FileUploader
      collectDate="2024-01-01"
      linbanId="1"
      linbanName="测试林班"
      collector="测试用户"
      remark="文件描述"
      onUploadComplete={handleUploadComplete}
      onUploadError={handleUploadError}
    />
  );
};
```

### 高级配置

```tsx
<FileUploader
  collectDate="2024-01-01"
  linbanId="1"
  linbanName="测试林班"
  collector="测试用户"
  remark="文件描述"
  
  // 文件限制
  maxFileSize={500 * 1024 * 1024} // 500MB
  allowedTypes={['jpg', 'png', 'pdf', 'geojson']}
  
  // 分片上传配置
  enableChunkUpload={true}
  chunkUploadThreshold={10 * 1024 * 1024} // 10MB启用分片上传
  
  // 回调函数
  onUploadProgress={(fileId, progress) => console.log(progress)}
  onUploadComplete={(fileId, fileUrl) => console.log(fileUrl)}
  onUploadError={(fileId, error) => console.error(error)}
/>
```

### 使用Hook

```tsx
import { useChunkUpload } from '@/hooks/useChunkUpload';

const MyComponent = () => {
  const chunkUpload = useChunkUpload({
    maxConcurrency: 3,
    enableRetry: true,
    onProgress: (task) => console.log(task.progress),
    onSuccess: (task, fileUrl) => console.log(fileUrl),
    onError: (task, error) => console.error(error),
  });

  const handleFileSelect = async (file: File) => {
    try {
      const fileUrl = await chunkUpload.uploadFile(file, {
        path: 'uploads/2024/'
      });
      console.log('上传成功:', fileUrl);
    } catch (error) {
      console.error('上传失败:', error);
    }
  };

  return (
    <div>
      <input type="file" onChange={(e) => {
        const file = e.target.files?.[0];
        if (file) handleFileSelect(file);
      }} />
      
      {/* 显示上传任务 */}
      {chunkUpload.getAllTasks().map(task => (
        <div key={task.id}>
          <span>{task.file.name}</span>
          <span>{task.progress}%</span>
          <button onClick={() => chunkUpload.pauseUpload(task.id)}>暂停</button>
          <button onClick={() => chunkUpload.resumeUpload(task.id)}>恢复</button>
          <button onClick={() => chunkUpload.cancelUpload(task.id)}>取消</button>
        </div>
      ))}
    </div>
  );
};
```

## 配置参数

### FileUploader Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| collectDate | string | - | 采集日期 |
| linbanId | string | - | 林班ID |
| linbanName | string | - | 林班名称 |
| collector | string | - | 采集人 |
| remark | string | - | 备注信息 |
| maxFileSize | number | 100MB | 最大文件大小 |
| allowedTypes | string[] | - | 允许的文件类型 |
| enableChunkUpload | boolean | true | 是否启用分片上传 |
| chunkUploadThreshold | number | 10MB | 分片上传阈值 |
| onUploadProgress | function | - | 上传进度回调 |
| onUploadComplete | function | - | 上传完成回调 |
| onUploadError | function | - | 上传错误回调 |

### useChunkUpload Options

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| chunkSize | number | 自动计算 | 分片大小 |
| maxConcurrency | number | 3 | 最大并发数 |
| enableRetry | boolean | true | 是否启用重试 |
| retryConfig | object | - | 重试配置 |
| onProgress | function | - | 进度回调 |
| onSuccess | function | - | 成功回调 |
| onError | function | - | 错误回调 |
| onStatusChange | function | - | 状态变化回调 |

## 最佳实践

### 1. 分片大小选择
- 小文件（< 10MB）：1MB分片
- 中等文件（10MB - 100MB）：2-5MB分片
- 大文件（> 100MB）：5-10MB分片

### 2. 并发控制
- 建议并发数不超过3-5个
- 根据网络状况调整并发数

### 3. 错误处理
- 启用自动重试机制
- 使用指数退避策略
- 提供用户友好的错误信息

### 4. 用户体验
- 显示详细的上传进度
- 提供暂停/恢复功能
- 支持批量上传
- 清理已完成的任务

## 故障排除

### 常见问题

1. **上传失败**
   - 检查网络连接
   - 验证文件大小和类型
   - 查看控制台错误信息

2. **进度不更新**
   - 检查回调函数是否正确设置
   - 确认API响应格式正确

3. **断点续传不工作**
   - 确认uploadId正确保存
   - 检查服务端是否支持断点续传

4. **内存占用过高**
   - 减少并发数
   - 调整分片大小
   - 及时清理已完成任务

### 调试技巧

1. 开启控制台日志
2. 使用网络面板查看请求
3. 检查API响应格式
4. 验证文件MD5值

## 更新日志

### v2.0.0
- 重新设计文件上传架构
- 支持分片上传和断点续传
- 添加并发控制和进度监控
- 优化用户界面和交互体验
- 完善错误处理和重试机制

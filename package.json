{"name": "OpenCorp-Base", "version": "1.0.0", "main": "dist-electron/main/index.js", "description": "OpenIM PC Client.", "author": "blooming", "private": true, "debug": {"env": {"VITE_DEV_SERVER_URL": "http://127.0.0.1:7777/"}}, "scripts": {"dev": "vite --force --host", "dev:web": "cross-env VITE_DISABLE_ELECTRON=true vite --force --host", "build": "vite build", "preview": "vite preview", "pree2e": "vite build --mode=test", "e2e": "playwright test", "format": "prettier --write .", "lint": "eslint --ext .js,.jsx,.ts,.tsx src", "lint:fix": "eslint --ext .js,.jsx,.ts,.tsx --fix --quiet src", "prepare": "husky install", "postinstall": "patch-package"}, "devDependencies": {"@commitlint/cli": "^17.4.4", "@commitlint/config-conventional": "^17.4.4", "@playwright/test": "^1.31.0", "@types/md5": "^2.3.2", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/uuid": "^9.0.1", "@typescript-eslint/eslint-plugin": "^5.53.0", "@typescript-eslint/parser": "^5.53.0", "@vitejs/plugin-legacy": "^4.1.0", "@vitejs/plugin-react": "^3.1.0", "autoprefixer": "^10.4.13", "cross-env": "^7.0.3", "electron": "^22.3.27", "electron-builder": "^23.6.0", "eslint": "^8.34.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-simple-import-sort": "^10.0.0", "husky": "^8.0.3", "lint-staged": "^13.1.2", "patch-package": "^8.0.0", "postcss": "^8.4.21", "postcss-nesting": "^12.0.2", "prettier": "^2.8.4", "prettier-plugin-tailwindcss": "^0.2.3", "react": "^18.2.0", "react-dom": "^18.2.0", "sass": "^1.58.3", "tailwindcss": "^3.2.7", "terser": "^5.19.0", "typescript": "^4.9.5", "vite": "^4.1.4", "vite-electron-plugin": "^0.8.2", "vite-plugin-electron": "^0.11.1"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "lint-staged": {"src/**/*.{tsx,ts}": ["prettier --write", "eslint --fix"], "*.{json,html,css,scss,xml,md}": ["prettier --write"]}, "dependencies": {"@ant-design/icons": "^5.1.4", "@ckeditor/ckeditor5-react": "^8.0.0", "@livekit/components-react": "2.6.9", "@livekit/components-styles": "1.1.4", "@openim/electron-client-sdk": "^3.8.3-patch.3", "@openim/wasm-client-sdk": "^3.8.3-patch.3", "@tmcw/togeojson": "^7.1.2", "@types/leaflet": "^1.9.18", "@types/leaflet-draw": "^1.0.12", "@xmldom/xmldom": "^0.9.8", "adm-zip": "^0.5.10", "ahooks": "^3.7.7", "antd": "5.10.0", "axios": "^1.4.0", "ckeditor5": "^43.0.0", "clsx": "^1.2.1", "date-fns": "^2.30.0", "dayjs": "^1.11.7", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "electron-log": "^5.0.0", "electron-screenshots": "^0.5.23", "electron-store": "^8.1.0", "electron-updater": "^5.3.0", "emoji-picker-element": "1.21.3", "i18next": "^22.5.0", "i18next-browser-languagedetector": "^7.0.2", "i18next-fs-backend": "^2.2.0", "leaflet": "^1.9.4", "leaflet-draw": "^1.0.4", "livekit-client": "2.7.0", "localforage": "^1.10.0", "md5": "^2.3.0", "mitt": "^3.0.0", "ol": "^10.5.0", "react-draggable": "^4.4.5", "react-i18next": "^12.3.1", "react-image-file-resizer": "^0.4.8", "react-intersection-observer": "^9.16.0", "react-leaflet": "^5.0.0", "react-query": "^3.39.3", "react-resizable-panels": "^2.0.2", "react-router-dom": "^6.11.1", "react-use": "^17.4.0", "react-virtuoso": "4.3.8", "sudo-prompt": "^9.2.1", "twemoji": "14.0.1", "uuid": "^9.0.1", "xgplayer": "^3.0.5", "zustand": "^4.3.3"}}